#!/usr/bin/env python3
"""
Реальный тест скрапинга данных с FlashScore
Запускаем скрапер и смотрим, какие данные он собирает
"""

import os
import sqlite3
import time
from pathlib import Path

def setup_test_environment():
    """Настройка тестового окружения"""
    print("🔧 Настройка тестового окружения...")
    
    # Создаем директории
    os.makedirs('database', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Создаем конфигурационный файл для тестирования
    config_content = """
# Тестовая конфигурация для FlashScore
football:
  - "https://www.flashscore.com/football/"
"""
    
    with open('config/test_urls.yaml', 'w') as f:
        f.write(config_content)
    
    print("✅ Тестовое окружение готово")

def test_match_id_scraper():
    """Тестируем MatchIDScraper - сбор ID матчей"""
    print("\n🎯 Тестируем MatchIDScraper...")
    
    try:
        from flashscore_scraper import MatchIDScraper
        
        # Создаем скрапер
        scraper = MatchIDScraper(
            config_path='config/test_urls.yaml',
            db_path='database/test_match_ids.db'
        )
        
        print("✅ MatchIDScraper создан")
        
        # ВНИМАНИЕ: Это реальный запрос к FlashScore!
        print("⚠️  Запускаем реальный сбор данных (это может занять время)...")
        
        # Запускаем сбор с ограничением
        result = scraper.scrape()
        
        print(f"✅ Результат сбора: {result}")
        
        # Проверяем, что собрали
        check_database_content('database/test_match_ids.db', 'ID матчей')
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в MatchIDScraper: {e}")
        return False

def test_match_data_scraper():
    """Тестируем MatchDataScraper - сбор данных матчей"""
    print("\n📊 Тестируем MatchDataScraper...")
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        # Создаем скрапер
        scraper = MatchDataScraper(db_path='database/test_match_data.db')
        
        print("✅ MatchDataScraper создан")
        
        # Пробуем обновить данные за последний день (меньше нагрузки)
        print("⚠️  Запускаем сбор данных матчей за последний день...")
        
        result = scraper.update(days=1, batch_size=10, headless=True)
        
        print(f"✅ Результат сбора: {result}")
        
        # Проверяем, что собрали
        check_database_content('database/test_match_data.db', 'данные матчей')
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в MatchDataScraper: {e}")
        return False

def test_odds_scraper():
    """Тестируем OddsDataScraper - сбор коэффициентов"""
    print("\n💰 Тестируем OddsDataScraper...")
    
    try:
        from flashscore_scraper import OddsDataScraper
        
        # Создаем скрапер
        scraper = OddsDataScraper(db_path='database/test_odds.db')
        
        print("✅ OddsDataScraper создан")
        
        # Пробуем собрать коэффициенты
        print("⚠️  Запускаем сбор коэффициентов...")
        
        result = scraper.scrape()
        
        print(f"✅ Результат сбора: {result}")
        
        # Проверяем, что собрали
        check_database_content('database/test_odds.db', 'коэффициенты')
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в OddsDataScraper: {e}")
        return False

def check_database_content(db_path, data_type):
    """Проверяем содержимое базы данных"""
    print(f"\n🔍 Проверяем содержимое базы данных ({data_type})...")
    
    if not os.path.exists(db_path):
        print(f"❌ База данных {db_path} не найдена")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получаем список таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print("📝 База данных пуста - таблицы не созданы")
            conn.close()
            return
        
        print(f"📊 Найдено таблиц: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n🗂️  Таблица: {table_name}")
            
            # Получаем структуру таблицы
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("   Колонки:")
            for col in columns:
                col_name, col_type = col[1], col[2]
                print(f"   • {col_name} ({col_type})")
            
            # Получаем количество записей
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"   📈 Записей: {count}")
            
            # Показываем несколько примеров записей
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                rows = cursor.fetchall()
                
                print("   📋 Примеры данных:")
                for i, row in enumerate(rows, 1):
                    print(f"   {i}. {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Ошибка при проверке базы данных: {e}")

def show_summary():
    """Показываем итоговую сводку"""
    print("\n" + "="*60)
    print("📋 ИТОГОВАЯ СВОДКА СОБРАННЫХ ДАННЫХ")
    print("="*60)
    
    # Проверяем все созданные базы данных
    db_files = [
        ('database/test_match_ids.db', 'ID матчей'),
        ('database/test_match_data.db', 'Данные матчей'),
        ('database/test_odds.db', 'Коэффициенты')
    ]
    
    for db_path, description in db_files:
        if os.path.exists(db_path):
            print(f"\n📊 {description} ({db_path}):")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                total_records = 0
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                    count = cursor.fetchone()[0]
                    total_records += count
                    print(f"   • {table_name}: {count} записей")
                
                print(f"   🎯 Всего записей: {total_records}")
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Ошибка: {e}")
        else:
            print(f"\n❌ {description}: база данных не создана")

def main():
    """Главная функция тестирования"""
    print("🚀 РЕАЛЬНЫЙ ТЕСТ FLASHSCORE SCRAPER")
    print("="*50)
    print("⚠️  ВНИМАНИЕ: Это реальные запросы к FlashScore!")
    print("Процесс может занять несколько минут...")
    print("="*50)
    
    # Настройка окружения
    setup_test_environment()
    
    # Тестируем каждый скрапер
    results = []
    
    print("\n🎯 Начинаем тестирование скраперов...")
    
    # Тест 1: MatchIDScraper (самый безопасный)
    try:
        result1 = test_match_id_scraper()
        results.append(('MatchIDScraper', result1))
    except Exception as e:
        print(f"❌ Критическая ошибка в MatchIDScraper: {e}")
        results.append(('MatchIDScraper', False))
    
    # Небольшая пауза между запросами
    print("\n⏳ Пауза 5 секунд между запросами...")
    time.sleep(5)
    
    # Тест 2: MatchDataScraper
    try:
        result2 = test_match_data_scraper()
        results.append(('MatchDataScraper', result2))
    except Exception as e:
        print(f"❌ Критическая ошибка в MatchDataScraper: {e}")
        results.append(('MatchDataScraper', False))
    
    # Пауза
    print("\n⏳ Пауза 5 секунд между запросами...")
    time.sleep(5)
    
    # Тест 3: OddsDataScraper
    try:
        result3 = test_odds_scraper()
        results.append(('OddsDataScraper', result3))
    except Exception as e:
        print(f"❌ Критическая ошибка в OddsDataScraper: {e}")
        results.append(('OddsDataScraper', False))
    
    # Показываем итоги
    print("\n" + "="*50)
    print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    for scraper_name, success in results:
        status = "✅ Успешно" if success else "❌ Ошибка"
        print(f"   • {scraper_name}: {status}")
    
    # Показываем сводку данных
    show_summary()
    
    print("\n🎉 Тестирование завершено!")
    print("Теперь вы знаете, какие данные может собирать flashscore-scraper!")

if __name__ == "__main__":
    main()
