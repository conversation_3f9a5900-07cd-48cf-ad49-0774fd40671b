#!/usr/bin/env python3
"""
Простой скрипт - реальный сбор данных
"""

from flashscore_scraper import MatchDataScraper
import time

print("🚀 ПРОСТОЙ СБОР ДАННЫХ")
print("=" * 30)

# Создаем скрапер
scraper = MatchDataScraper(db_path='database/simple_test.db')
print("✅ Скрапер создан")

# Пробуем собрать данные
print("\n🔄 Сбор данных за последние 3 дня...")
try:
    result = scraper.update(days=3, batch_size=10, headless=True)
    print(f"✅ Результат: {result}")
except Exception as e:
    print(f"❌ Ошибка update: {e}")

print("\n🔄 Прямой scrape...")
try:
    result = scraper.scrape(batch_size=5, headless=True)
    print(f"✅ Результат: {result}")
except Exception as e:
    print(f"❌ Ошибка scrape: {e}")

print("\n🔄 Предстоящие матчи...")
try:
    result = scraper.update_upcoming_fixtures(days=7, batch_size=5, headless=True)
    print(f"✅ Результат: {result}")
except Exception as e:
    print(f"❌ Ошибка upcoming: {e}")

print("\n🔍 Проверяем базу данных...")
try:
    import sqlite3
    import os

    db_path = 'database/simple_test.db'
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        print(f"📊 Таблиц: {len(tables)}")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"   • {table_name}: {count} записей")

        conn.close()
    else:
        print("❌ База данных не создана")

except Exception as e:
    print(f"❌ Ошибка проверки БД: {e}")

print("\n✅ Готово!")
