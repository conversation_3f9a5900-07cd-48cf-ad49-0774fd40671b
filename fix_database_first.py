#!/usr/bin/env python3
"""
ИСПРАВЛЯЕМ ПРОБЛЕМУ: Сначала заполняем базу данных, потом используем data loaders
"""

import os
import sqlite3

def register_sports_manually():
    """Регистрируем виды спорта в базе данных вручную"""
    print("🏗️ РЕГИСТРИРУЕМ ВИДЫ СПОРТА В БАЗЕ ДАННЫХ")
    print("=" * 45)
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        # Создаем скрапер для доступа к БД
        scraper = MatchDataScraper(db_path='database/sports_registered.db')

        # Используем прямое подключение к SQLite
        import sqlite3
        conn = sqlite3.connect('database/sports_registered.db')
        cursor = conn.cursor()
        
        # Регистрируем виды спорта
        sports_to_register = [
            (1, 'football'),
            (2, 'handball'), 
            (3, 'volleyball'),
            (4, 'basketball')
        ]
        
        for sport_id, sport_name in sports_to_register:
            cursor.execute("""
                INSERT OR IGNORE INTO sport_ids (id, name, created_at)
                VALUES (?, ?, datetime('now'))
            """, (sport_id, sport_name))
        
        conn.commit()

        # Проверяем что добавилось
        cursor.execute("SELECT * FROM sport_ids;")
        registered_sports = cursor.fetchall()

        print(f"✅ Зарегистрировано видов спорта: {len(registered_sports)}")
        for sport in registered_sports:
            print(f"   • ID {sport[0]}: {sport[1]}")

        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка регистрации спортов: {e}")
        return False

def test_data_loaders_after_registration():
    """Тестируем data loaders после регистрации спортов"""
    print("\n⚽ ТЕСТИРУЕМ DATA LOADERS ПОСЛЕ РЕГИСТРАЦИИ")
    print("=" * 45)
    
    # Указываем путь к БД с зарегистрированными спортами
    db_path = 'database/sports_registered.db'
    
    try:
        from flashscore_scraper.data_loaders import Football
        
        # Создаем loader с указанием БД
        loader = Football(db_path=db_path)
        print("✅ Football loader создан с правильной БД")
        
        try:
            df = loader.load_matches(
                league="Kazakhstan Premier League",
                seasons=["2024"],
                include_additional_data=True
            )
            print(f"✅ load_matches работает: {len(df)} записей")
            
            if len(df) > 0:
                print(f"   Колонки: {list(df.columns)}")
            else:
                print("   ⚠️  Матчей нет, но ошибки 'sport not registered' больше нет!")
                
        except Exception as e:
            print(f"❌ load_matches: {e}")
        
    except Exception as e:
        print(f"❌ Football loader: {e}")

def add_sample_match_data():
    """Добавляем примеры матчей для тестирования"""
    print("\n📊 ДОБАВЛЯЕМ ПРИМЕРЫ МАТЧЕЙ")
    print("=" * 30)
    
    try:
        import json
        from datetime import datetime
        
        conn = sqlite3.connect('database/sports_registered.db')
        cursor = conn.cursor()
        
        # Добавляем примеры матчей
        sample_matches = [
            {
                'flashscore_id': 'KZ_TEST_001',
                'sport_id': 1,  # football
                'country': 'Kazakhstan',
                'league': 'Kazakhstan Premier League',
                'season': 2024,
                'datetime': '2024-08-01 19:00:00',
                'home_team': 'FC Astana',
                'away_team': 'FC Kairat',
                'home_goals': 2,
                'away_goals': 1,
                'result': 1,
                'additional_data': json.dumps({
                    'events': [
                        {'minute': 23, 'type': 'penalty_goal', 'player': 'Мусаев', 'team': 'home'},
                        {'minute': 67, 'type': 'goal', 'player': 'Петров', 'team': 'away'},
                        {'minute': 89, 'type': 'goal', 'player': 'Иванов', 'team': 'home'}
                    ],
                    'penalties': [
                        {'minute': 23, 'player': 'Мусаев', 'result': 'goal'}
                    ]
                })
            },
            {
                'flashscore_id': 'KZ_TEST_002',
                'sport_id': 1,  # football
                'country': 'Kazakhstan',
                'league': 'Kazakhstan Premier League',
                'season': 2024,
                'datetime': '2024-08-05 16:30:00',
                'home_team': 'FC Shakhter',
                'away_team': 'FC Tobol',
                'home_goals': 1,
                'away_goals': 2,
                'result': 2,
                'additional_data': json.dumps({
                    'events': [
                        {'minute': 34, 'type': 'goal', 'player': 'Алиев', 'team': 'home'},
                        {'minute': 56, 'type': 'penalty_goal', 'player': 'Нуров', 'team': 'away'},
                        {'minute': 78, 'type': 'goal', 'player': 'Касымов', 'team': 'away'}
                    ],
                    'penalties': [
                        {'minute': 56, 'player': 'Нуров', 'result': 'goal'}
                    ]
                })
            }
        ]
        
        for match in sample_matches:
            cursor.execute("""
                INSERT OR REPLACE INTO match_data 
                (flashscore_id, sport_id, country, league, season, datetime, 
                 home_team, away_team, home_goals, away_goals, result, additional_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                match['flashscore_id'], match['sport_id'], match['country'],
                match['league'], match['season'], match['datetime'],
                match['home_team'], match['away_team'], match['home_goals'],
                match['away_goals'], match['result'], match['additional_data']
            ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Добавлено {len(sample_matches)} примеров матчей")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка добавления матчей: {e}")
        return False

def test_full_workflow():
    """Тестируем полный рабочий процесс"""
    print("\n🔄 ТЕСТИРУЕМ ПОЛНЫЙ РАБОЧИЙ ПРОЦЕСС")
    print("=" * 40)
    
    db_path = 'database/sports_registered.db'
    
    try:
        from flashscore_scraper.data_loaders import Football
        
        loader = Football(db_path=db_path)
        print("✅ Football loader создан")
        
        # Загружаем матчи
        df = loader.load_matches(
            league="Kazakhstan Premier League",
            seasons=["2024"],
            include_additional_data=True
        )
        
        print(f"✅ Загружено матчей: {len(df)}")
        
        if len(df) > 0:
            print("\n📊 ДАННЫЕ МАТЧЕЙ:")
            print(f"   Колонки: {list(df.columns)}")
            print(f"   Первые записи:")
            print(df.head())
            
            # Ищем пенальти в additional_data
            print(f"\n🥅 ПОИСК ПЕНАЛЬТИ:")
            penalty_matches = 0
            
            for idx, row in df.iterrows():
                if 'additional_data' in row and row['additional_data']:
                    additional_str = str(row['additional_data']).lower()
                    if 'penalty' in additional_str:
                        penalty_matches += 1
                        print(f"   ✅ Пенальти найдены: {row['home_team']} vs {row['away_team']}")
            
            print(f"   📈 Всего матчей с пенальти: {penalty_matches}")
            
        return len(df) > 0
        
    except Exception as e:
        print(f"❌ Ошибка полного процесса: {e}")
        return False

def main():
    """Главная функция исправления проблемы"""
    print("🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ 'SPORT NOT REGISTERED'")
    print("=" * 60)
    print("Проблема: Нужно сначала заполнить БД, потом использовать loaders")
    print("=" * 60)
    
    # 1. Регистрируем виды спорта
    sports_registered = register_sports_manually()
    
    if sports_registered:
        # 2. Тестируем data loaders после регистрации
        test_data_loaders_after_registration()
        
        # 3. Добавляем примеры матчей
        matches_added = add_sample_match_data()
        
        if matches_added:
            # 4. Тестируем полный рабочий процесс
            workflow_success = test_full_workflow()
            
            if workflow_success:
                print("\n🎉 УСПЕХ! ПРОБЛЕМА РЕШЕНА!")
                print("\n✅ ЧТО РАБОТАЕТ:")
                print("   • Виды спорта зарегистрированы в БД")
                print("   • Data loaders работают без ошибок")
                print("   • Матчи загружаются корректно")
                print("   • Пенальти находятся в additional_data")
                
                print("\n📋 СЛЕДУЮЩИЕ ШАГИ:")
                print("   1. Использовать реальные скраперы для сбора данных")
                print("   2. Применить этот же подход к реальной БД")
                print("   3. Искать пенальти в загруженных матчах")
            else:
                print("\n❌ Workflow не работает")
        else:
            print("\n❌ Не удалось добавить примеры матчей")
    else:
        print("\n❌ Не удалось зарегистрировать виды спорта")

if __name__ == "__main__":
    main()
