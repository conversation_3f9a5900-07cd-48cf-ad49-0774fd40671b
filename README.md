# FlashScore Scraper Project

Проект для изучения и использования библиотеки `flashscore-scraper` для сбора спортивных данных с FlashScore.

## 🚀 Быстрый старт

### Установка

1. Клонируйте репозиторий или создайте новую директорию
2. Создайте виртуальное окружение:
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate     # Windows
```

3. Установите библиотеку:
```bash
pip install flashscore-scraper
```

### Первый запуск

Запустите демонстрационный скрипт:
```bash
python simple_scraping_example.py
```

## 📊 Возможности сбора данных

### Типы данных

1. **ID матчей** - уникальные идентификаторы матчей
2. **Результаты матчей** - счет, команды, дата, лига
3. **Букмекерские коэффициенты** - коэффициенты различных букмекеров
4. **Расписание матчей** - предстоящие игры
5. **Статистика матчей** - детальная информация о ходе игры

### Поддерживаемые виды спорта

- ⚽ **Футбол** - самый популярный вид спорта
- 🤾 **Гандбол** - командный вид спорта
- 🏐 **Волейбол** - игра через сетку
- 🏀 **Баскетбол** - через конфигурацию
- 🎾 **Теннис** - через конфигурацию

## 🛠️ Основные классы

### MatchIDScraper
Сбор ID матчей с FlashScore:
```python
from flashscore_scraper import MatchIDScraper

scraper = MatchIDScraper(
    config_path='config/flashscore_urls.yaml',
    db_path='database/match_ids.db'
)
result = scraper.scrape()
```

### MatchDataScraper
Сбор детальных данных матчей:
```python
from flashscore_scraper import MatchDataScraper

scraper = MatchDataScraper(db_path='database/match_data.db')
result = scraper.scrape(batch_size=100, headless=True)
```

### OddsDataScraper
Сбор букмекерских коэффициентов:
```python
from flashscore_scraper import OddsDataScraper

scraper = OddsDataScraper(db_path='database/odds_data.db')
result = scraper.scrape()
```

## 📋 Модели данных

### MatchResult
```python
{
    'country': str,          # Страна
    'league': str,           # Лига/турнир
    'season': int,           # Сезон
    'datetime': str,         # Дата и время матча
    'home_team': str,        # Домашняя команда
    'away_team': str,        # Гостевая команда
    'home_goals': int,       # Голы домашней команды
    'away_goals': int,       # Голы гостевой команды
    'result': int,           # Результат (1-дома, 0-ничья, 2-гости)
    'sport_id': int,         # ID вида спорта
    'flashscore_id': str     # Уникальный ID матча
}
```

### MatchOdds
```python
{
    'flashscore_id': str,    # ID матча
    'sport_id': int,         # ID вида спорта
    'bookmaker': str,        # Название букмекера
    'home_odds': float,      # Коэффициент на победу дома
    'draw_odds': float,      # Коэффициент на ничью
    'away_odds': float       # Коэффициент на победу гостей
}
```

## 🔄 Рабочий процесс

1. **Настройка конфигурации** - создание файла с URL
2. **Сбор ID матчей** - получение списка матчей
3. **Сбор данных матчей** - детальная информация
4. **Сбор коэффициентов** - букмекерские данные
5. **Анализ данных** - обработка собранной информации

## ⚙️ Конфигурация

Создайте файл `config/flashscore_urls.yaml`:
```yaml
football:
  - "https://www.flashscore.com/football/"
  
basketball:
  - "https://www.flashscore.com/basketball/"
  
tennis:
  - "https://www.flashscore.com/tennis/"
```

## 💾 База данных

Библиотека использует SQLite для хранения данных:
- `database/match_ids.db` - ID матчей
- `database/match_data.db` - данные матчей
- `database/odds_data.db` - коэффициенты

## 🔧 Технические особенности

### Зависимости
- **Selenium** - автоматизация браузера
- **BeautifulSoup4** - парсинг HTML
- **Pandas** - работа с данными
- **Pydantic** - валидация данных
- **SQLite** - хранение данных

### Производительность
- Headless режим браузера
- Ограничение скорости запросов
- Пакетная обработка
- Автоматическое управление ресурсами

## 📁 Структура проекта

```
flashscore/
├── venv/                    # Виртуальное окружение
├── database/                # База данных SQLite
├── config/                  # Конфигурационные файлы
├── explore_flashscore.py    # Изучение библиотеки
├── flashscore_example.py    # Подробные примеры
├── simple_scraping_example.py # Простой пример
└── README.md               # Документация
```

## 🎯 Примеры использования

### Обновление данных за последние 3 дня
```python
scraper = MatchDataScraper()
result = scraper.update(days=3, batch_size=50)
print(f"Обновлено матчей: {result}")
```

### Сбор предстоящих матчей
```python
scraper = MatchDataScraper()
result = scraper.update_upcoming_fixtures(days=7)
print(f"Загружено предстоящих матчей: {result}")
```

## ⚠️ Важные замечания

1. **Соблюдайте условия использования FlashScore**
2. **Не перегружайте серверы частыми запросами**
3. **Используйте headless режим для экономии ресурсов**
4. **Регулярно обновляйте данные, но с разумными интервалами**

## 🚀 Возможности для развития

- Добавление новых видов спорта
- Интеграция с аналитическими инструментами
- Создание веб-интерфейса
- Автоматизация сбора данных
- Машинное обучение на собранных данных

## 📞 Поддержка

Если у вас возникли вопросы или проблемы:
1. Проверьте документацию библиотеки
2. Убедитесь, что все зависимости установлены
3. Проверьте конфигурационные файлы
4. Используйте демонстрационные скрипты для тестирования
