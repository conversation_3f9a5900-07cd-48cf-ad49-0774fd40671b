#!/usr/bin/env python3
"""
Простой пример использования flashscore-scraper для сбора данных
ВНИМАНИЕ: Этот пример предназначен только для демонстрации возможностей
"""

import os
import time
from pathlib import Path

def setup_environment():
    """Настройка окружения"""
    print("🔧 Настройка окружения...")
    
    # Создаем необходимые директории
    os.makedirs('database', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Создаем базовый конфигурационный файл
    config_content = """
# Базовая конфигурация для FlashScore URLs
football:
  - "https://www.flashscore.com/football/"
  
basketball:
  - "https://www.flashscore.com/basketball/"
"""
    
    config_path = Path('config/flashscore_urls.yaml')
    if not config_path.exists():
        with open(config_path, 'w') as f:
            f.write(config_content)
        print("✅ Конфигурационный файл создан")
    else:
        print("✅ Конфигурационный файл уже существует")

def demonstrate_basic_usage():
    """Демонстрация базового использования"""
    print("\n📊 Демонстрация базового использования библиотеки")
    
    try:
        from flashscore_scraper import MatchDataScraper, MatchIDScraper, OddsDataScraper
        
        print("\n1️⃣ Создание MatchIDScraper")
        id_scraper = MatchIDScraper(
            config_path='config/flashscore_urls.yaml',
            db_path='database/match_ids.db'
        )
        print("✅ MatchIDScraper создан успешно")
        
        print("\n2️⃣ Создание MatchDataScraper")
        data_scraper = MatchDataScraper(db_path='database/match_data.db')
        print("✅ MatchDataScraper создан успешно")
        
        print("\n3️⃣ Создание OddsDataScraper")
        odds_scraper = OddsDataScraper(db_path='database/odds_data.db')
        print("✅ OddsDataScraper создан успешно")
        
        # Демонстрация получения компонентов
        print("\n🔍 Изучение компонентов:")
        
        # Получаем браузер-менеджер
        browser = data_scraper.get_browser(headless=True)
        print("✅ Браузер-менеджер получен (headless режим)")
        
        # Получаем менеджер базы данных
        db = data_scraper.get_database()
        print("✅ Менеджер базы данных получен")
        
        # Закрываем соединения
        browser.close()
        db.close()
        print("✅ Соединения закрыты")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False

def show_data_models():
    """Показать модели данных"""
    print("\n📋 Модели данных, доступные в библиотеке:")
    
    try:
        from flashscore_scraper.models import MatchResult, MatchOdds, FlashscoreConfig
        
        print("\n🏆 MatchResult - модель результата матча:")
        print("   • country: страна")
        print("   • league: лига/турнир")
        print("   • season: сезон")
        print("   • datetime: дата и время матча")
        print("   • home_team: домашняя команда")
        print("   • away_team: гостевая команда")
        print("   • home_goals: голы домашней команды")
        print("   • away_goals: голы гостевой команды")
        print("   • result: результат (1-победа дома, 0-ничья, 2-победа гостей)")
        print("   • sport_id: ID вида спорта")
        print("   • flashscore_id: уникальный ID матча в FlashScore")
        
        print("\n💰 MatchOdds - модель коэффициентов:")
        print("   • flashscore_id: ID матча")
        print("   • sport_id: ID вида спорта")
        print("   • bookmaker: название букмекера")
        print("   • home_odds: коэффициент на победу дома")
        print("   • draw_odds: коэффициент на ничью")
        print("   • away_odds: коэффициент на победу гостей")
        
        print("\n⚙️ FlashscoreConfig - конфигурация:")
        print("   • sport_ids: словарь конфигураций для видов спорта")
        
    except Exception as e:
        print(f"❌ Ошибка при изучении моделей: {e}")

def show_scraping_workflow():
    """Показать рабочий процесс сбора данных"""
    print("\n🔄 Типичный рабочий процесс сбора данных:")
    
    print("\n1️⃣ Сбор ID матчей:")
    print("   • Используйте MatchIDScraper.scrape()")
    print("   • Сканирует FlashScore для поиска матчей")
    print("   • Сохраняет ID матчей в базу данных")
    
    print("\n2️⃣ Сбор детальных данных матчей:")
    print("   • Используйте MatchDataScraper.scrape()")
    print("   • Собирает подробную информацию о каждом матче")
    print("   • Включает счет, статистику, время проведения")
    
    print("\n3️⃣ Сбор коэффициентов:")
    print("   • Используйте OddsDataScraper.scrape()")
    print("   • Собирает букмекерские коэффициенты")
    print("   • Поддерживает различных букмекеров")
    
    print("\n4️⃣ Обновление данных:")
    print("   • Используйте update() методы")
    print("   • Автоматически обновляет данные за последние дни")
    print("   • Поддерживает инкрементальные обновления")

def show_supported_sports():
    """Показать поддерживаемые виды спорта"""
    print("\n⚽ Поддерживаемые виды спорта:")
    
    sports_info = {
        "Football": "Футбол - самый популярный вид спорта",
        "Handball": "Гандбол - командный вид спорта",
        "Volleyball": "Волейбол - игра через сетку"
    }
    
    for sport, description in sports_info.items():
        print(f"   • {sport}: {description}")
        print(f"     - Доступны методы: load_fixtures(), load_matches(), load_odds()")

def show_technical_details():
    """Показать технические детали"""
    print("\n🔧 Технические особенности:")
    
    print("\n📦 Зависимости:")
    print("   • Selenium - для автоматизации браузера")
    print("   • BeautifulSoup4 - для парсинга HTML")
    print("   • Pandas - для работы с данными")
    print("   • Pydantic - для валидации данных")
    print("   • SQLite - для хранения данных")
    
    print("\n⚡ Производительность:")
    print("   • Поддержка headless режима браузера")
    print("   • Встроенное ограничение скорости запросов")
    print("   • Пакетная обработка данных")
    print("   • Автоматическое управление браузером")
    
    print("\n💾 Хранение данных:")
    print("   • SQLite база данных")
    print("   • Структурированные таблицы")
    print("   • Автоматическое создание схемы")
    print("   • Поддержка SQL запросов")

def main():
    """Главная функция"""
    print("🏆 FlashScore Scraper - Практическое руководство")
    print("=" * 60)
    
    # Настройка окружения
    setup_environment()
    
    # Демонстрация базового использования
    if demonstrate_basic_usage():
        print("✅ Базовая демонстрация прошла успешно")
    else:
        print("❌ Ошибка в базовой демонстрации")
    
    # Показать модели данных
    show_data_models()
    
    # Показать рабочий процесс
    show_scraping_workflow()
    
    # Показать поддерживаемые виды спорта
    show_supported_sports()
    
    # Показать технические детали
    show_technical_details()
    
    print("\n" + "=" * 60)
    print("🎯 ЗАКЛЮЧЕНИЕ:")
    print("Библиотека flashscore-scraper предоставляет мощные инструменты")
    print("для сбора спортивных данных с FlashScore. Она подходит для:")
    print("• Спортивной аналитики")
    print("• Создания прогнозных моделей")
    print("• Мониторинга коэффициентов")
    print("• Исследования спортивных трендов")
    print("\n⚠️  ВАЖНО: Соблюдайте условия использования FlashScore")
    print("и не перегружайте их серверы частыми запросами!")

if __name__ == "__main__":
    main()
