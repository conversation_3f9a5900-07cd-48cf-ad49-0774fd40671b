{"package": "flashscore-scraper", "summary": "A Python package for scraping sports data from Flashscore, enabling data-driven sports analytics, visualization projects, and betting models.", "pypi_url": "https://pypi.org/project/flashscore-scraper", "piwheels_url": "https://www.piwheels.org/project/flashscore-scraper", "releases": {"0.0.7": {"released": "2025-03-19 08:49:13", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.7-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.7-py3-none-any.whl", "filehash": "b6d74a8b701bdecc82fa2a9d021b587dc499dcacfa8185d25fcb9566995740c0", "filesize": 46073, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.6": {"released": "2025-03-10 17:41:23", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.6-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.6-py3-none-any.whl", "filehash": "40e506298d5aad6e20073ec21157262e49cc7336031a3d8cbdaa5dd4f7f517af", "filesize": 45673, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.5": {"released": "2025-03-05 09:06:04", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.5-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.5-py3-none-any.whl", "filehash": "3bb1f0a6255531fbedb53591312d15cc07f05003a97f57ed80deed5dd39ca4ad", "filesize": 42721, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.4": {"released": "2025-03-04 20:24:25", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.4-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.4-py3-none-any.whl", "filehash": "e2fe18c1f65d63ba7dbd6fb0e48516ad87fac4164c07083ff070f076fe000e44", "filesize": 42734, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.3": {"released": "2025-03-04 20:21:16", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.3-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.3-py3-none-any.whl", "filehash": "e41c5dea68101923ed5573440e874aff5dcdb34bad4f5aaf6a126862217e6d0a", "filesize": 42734, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.2": {"released": "2025-03-04 18:18:40", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.2-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.2-py3-none-any.whl", "filehash": "6f785a61eecadcfc6e603663ff4c3eeb2b0b46599738ded195995f4db8daab46", "filesize": 42734, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}, "0.0.1": {"released": "2025-03-04 18:18:37", "prerelease": false, "yanked": false, "skip_reason": "", "files": {"flashscore_scraper-0.0.1-py3-none-any.whl": {"file_url": "https://archive1.piwheels.org/simple/flashscore-scraper/flashscore_scraper-0.0.1-py3-none-any.whl", "filehash": "c223f557d28b4ed3e03d08b7decaa6976321e18859deb129bf4dca2621a4535b", "filesize": 41019, "builder_abi": "cp311", "file_abi_tag": "none", "platform": "any", "requires_python": ">=3.10", "apt_dependencies": []}}}}}