#!/usr/bin/env python3
"""
Практический пример использования библиотеки flashscore-scraper
Демонстрирует основные возможности сбора спортивных данных
"""

import os
import sys
from pathlib import Path
from pprint import pprint
import sqlite3

def create_directories():
    """Создаем необходимые директории"""
    os.makedirs('database', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    print("✅ Директории созданы")

def explore_models():
    """Изучаем модели данных библиотеки"""
    print("\n=== Изучение моделей данных ===")
    
    try:
        from flashscore_scraper import models
        print("📦 Модуль models импортирован")
        
        # Изучаем доступные модели
        model_classes = [attr for attr in dir(models) if not attr.startswith('_')]
        print(f"Найдено моделей: {len(model_classes)}")
        
        for model_name in model_classes:
            model_obj = getattr(models, model_name)
            if hasattr(model_obj, '__annotations__'):
                print(f"\n🏗️  Модель: {model_name}")
                annotations = getattr(model_obj, '__annotations__', {})
                for field, field_type in annotations.items():
                    print(f"   • {field}: {field_type}")
            elif hasattr(model_obj, '__dict__'):
                print(f"\n📋 Объект: {model_name}")
                attrs = [attr for attr in dir(model_obj) if not attr.startswith('_')]
                for attr in attrs[:5]:  # Показываем первые 5 атрибутов
                    print(f"   • {attr}")
                    
    except Exception as e:
        print(f"❌ Ошибка при изучении моделей: {e}")

def demonstrate_match_id_scraper():
    """Демонстрация MatchIDScraper"""
    print("\n=== Демонстрация MatchIDScraper ===")
    
    try:
        from flashscore_scraper import MatchIDScraper
        
        # Создаем конфигурационный файл
        config_content = """
# Конфигурация URL для FlashScore
football:
  - "https://www.flashscore.com/football/"
  
basketball:
  - "https://www.flashscore.com/basketball/"
  
tennis:
  - "https://www.flashscore.com/tennis/"
"""
        
        with open('config/flashscore_urls.yaml', 'w') as f:
            f.write(config_content)
        
        print("✅ Конфигурационный файл создан")
        
        # Создаем экземпляр скрапера
        scraper = MatchIDScraper(
            config_path='config/flashscore_urls.yaml',
            db_path='database/matches.db'
        )
        
        print("✅ MatchIDScraper создан")
        print("📋 Методы скрапера:")
        methods = [method for method in dir(scraper) if not method.startswith('_') and callable(getattr(scraper, method))]
        for method in methods:
            print(f"   • {method}()")
            
        # Получаем базу данных для изучения структуры
        db = scraper.get_database()
        print("✅ Подключение к базе данных получено")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

def demonstrate_match_data_scraper():
    """Демонстрация MatchDataScraper"""
    print("\n=== Демонстрация MatchDataScraper ===")
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        scraper = MatchDataScraper(db_path='database/match_data.db')
        print("✅ MatchDataScraper создан")
        
        # Изучаем методы
        print("📋 Основные методы:")
        print("   • scrape() - основной метод для сбора данных матчей")
        print("   • update() - обновление данных за последние дни")
        print("   • update_upcoming_fixtures() - обновление предстоящих матчей")
        
        # Получаем браузер-менеджер
        browser = scraper.get_browser(headless=True)
        print("✅ Браузер-менеджер получен")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

def demonstrate_odds_scraper():
    """Демонстрация OddsDataScraper"""
    print("\n=== Демонстрация OddsDataScraper ===")
    
    try:
        from flashscore_scraper import OddsDataScraper
        
        scraper = OddsDataScraper(db_path='database/odds_data.db')
        print("✅ OddsDataScraper создан")
        print("📋 Назначение: сбор данных о букмекерских коэффициентах")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

def demonstrate_sport_classes():
    """Демонстрация классов для конкретных видов спорта"""
    print("\n=== Демонстрация классов видов спорта ===")
    
    sports = ['Football', 'Handball', 'Volleyball']
    
    for sport in sports:
        try:
            from flashscore_scraper import Football, Handball, Volleyball
            
            sport_class = globals()[sport] if sport in ['Football', 'Handball', 'Volleyball'] else None
            if sport_class:
                sport_class_obj = eval(sport)
                print(f"\n⚽ {sport}")
                print("   Методы:")
                print("   • load_fixtures() - загрузка расписания матчей")
                print("   • load_matches() - загрузка данных матчей")
                print("   • load_odds() - загрузка коэффициентов")
                print("   • process_additional_data() - обработка дополнительных данных")
                
        except Exception as e:
            print(f"❌ Ошибка для {sport}: {e}")

def explore_database_structure():
    """Изучаем структуру базы данных"""
    print("\n=== Структура базы данных ===")
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        scraper = MatchDataScraper(db_path='database/test.db')
        db = scraper.get_database()
        
        # Получаем курсор для выполнения SQL запросов
        cursor = db.get_cursor()
        
        # Получаем список таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if tables:
            print("📊 Найденные таблицы в базе данных:")
            for table in tables:
                table_name = table[0]
                print(f"\n🗂️  Таблица: {table_name}")
                
                # Получаем структуру таблицы
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                if columns:
                    print("   Колонки:")
                    for col in columns:
                        col_name, col_type = col[1], col[2]
                        print(f"   • {col_name} ({col_type})")
        else:
            print("📝 База данных пуста - таблицы будут созданы при первом использовании")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Ошибка при изучении базы данных: {e}")

def main():
    """Основная функция демонстрации"""
    print("🏆 Демонстрация возможностей flashscore-scraper")
    print("=" * 50)
    
    # Создаем необходимые директории
    create_directories()
    
    # Изучаем модели данных
    explore_models()
    
    # Демонстрируем основные классы
    demonstrate_match_id_scraper()
    demonstrate_match_data_scraper()
    demonstrate_odds_scraper()
    demonstrate_sport_classes()
    
    # Изучаем структуру базы данных
    explore_database_structure()
    
    print("\n" + "=" * 50)
    print("🎯 ВОЗМОЖНОСТИ СБОРА ДАННЫХ:")
    print("• ID матчей и основная информация")
    print("• Детальные данные матчей (счет, статистика)")
    print("• Букмекерские коэффициенты")
    print("• Данные по футболу, гандболу, волейболу")
    print("• Расписание предстоящих матчей")
    print("• Исторические данные матчей")
    print("\n🔧 ТЕХНИЧЕСКИЕ ОСОБЕННОСТИ:")
    print("• Использует Selenium для веб-скрапинга")
    print("• Сохраняет данные в SQLite базу данных")
    print("• Поддерживает headless режим браузера")
    print("• Имеет встроенное управление скоростью запросов")
    print("• Поддерживает пакетную обработку данных")

if __name__ == "__main__":
    main()
