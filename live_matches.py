#!/usr/bin/env python3
"""
LIVE МАТЧИ В РЕАЛЬНОМ ВРЕМЕНИ
Показывает текущие матчи используя flashscore-scraper
"""

import os
import time
import sqlite3
import json
from datetime import datetime
import threading

class LiveMatchMonitor:
    def __init__(self):
        self.running = False
        self.matches = {}
        self.setup_database()
    
    def setup_database(self):
        """Настройка базы данных для live матчей"""
        os.makedirs('database', exist_ok=True)
        self.db_path = 'database/live_matches.db'
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Создаем таблицу для live матчей
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS live_matches (
                    flashscore_id TEXT PRIMARY KEY,
                    home_team TEXT,
                    away_team TEXT,
                    home_score INTEGER DEFAULT 0,
                    away_score INTEGER DEFAULT 0,
                    minute TEXT,
                    status TEXT,
                    league TEXT,
                    country TEXT,
                    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    events TEXT
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ База данных для live матчей готова")
            
        except Exception as e:
            print(f"❌ Ошибка настройки БД: {e}")
    
    def collect_live_matches(self):
        """Собираем текущие live матчи"""
        try:
            from flashscore_scraper import MatchDataScraper
            
            scraper = MatchDataScraper(db_path=self.db_path)
            
            # Собираем текущие матчи
            result = scraper.update(days=1, batch_size=20, headless=True)
            
            return result
            
        except Exception as e:
            print(f"❌ Ошибка сбора live матчей: {e}")
            return {}
    
    def get_live_matches_from_db(self):
        """Получаем live матчи из базы данных"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Получаем все матчи из сегодняшнего дня
            cursor.execute("""
                SELECT flashscore_id, home_team, away_team, home_goals, away_goals, 
                       datetime, league, country, additional_data
                FROM match_data 
                WHERE date(datetime) = date('now')
                ORDER BY datetime DESC
                LIMIT 20
            """)
            
            matches = cursor.fetchall()
            conn.close()
            
            live_matches = []
            for match in matches:
                (match_id, home, away, h_goals, a_goals, dt, league, country, add_data) = match
                
                # Определяем статус матча
                status = self.determine_match_status(add_data, dt)
                
                live_match = {
                    'id': match_id,
                    'home_team': home,
                    'away_team': away,
                    'home_score': h_goals or 0,
                    'away_score': a_goals or 0,
                    'datetime': dt,
                    'league': league,
                    'country': country,
                    'status': status,
                    'events': self.extract_events(add_data)
                }
                
                live_matches.append(live_match)
            
            return live_matches
            
        except Exception as e:
            print(f"❌ Ошибка получения матчей: {e}")
            return []
    
    def determine_match_status(self, additional_data, datetime_str):
        """Определяем статус матча"""
        now = datetime.now()
        
        try:
            match_time = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
            time_diff = (now - match_time).total_seconds() / 60  # в минутах
            
            if time_diff < -30:
                return "ПРЕДСТОЯЩИЙ"
            elif -30 <= time_diff <= 0:
                return "СКОРО"
            elif 0 < time_diff <= 105:  # 90 мин + добавленное время
                return f"LIVE {int(time_diff)}'"
            elif time_diff > 105:
                return "ЗАВЕРШЕН"
            else:
                return "НЕИЗВЕСТНО"
                
        except:
            return "НЕИЗВЕСТНО"
    
    def extract_events(self, additional_data):
        """Извлекаем события матча"""
        if not additional_data:
            return []
        
        try:
            if isinstance(additional_data, str):
                data = json.loads(additional_data)
            else:
                data = additional_data
            
            events = []
            
            # Ищем события в разных форматах
            if 'events' in data:
                events.extend(data['events'])
            
            if 'match_events' in data:
                events.extend(data['match_events'])
            
            if 'goals' in data:
                events.extend(data['goals'])
            
            return events[:5]  # Последние 5 событий
            
        except:
            return []
    
    def display_live_matches(self, matches):
        """Отображаем live матчи"""
        os.system('clear' if os.name == 'posix' else 'cls')  # Очищаем экран
        
        print("🔴 LIVE МАТЧИ - FLASHSCORE")
        print("=" * 60)
        print(f"🕐 Обновлено: {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 60)
        
        if not matches:
            print("❌ Live матчи не найдены")
            print("💡 Попробуйте:")
            print("   • Собрать больше данных")
            print("   • Проверить подключение к интернету")
            print("   • Запустить в другое время")
            return
        
        live_count = 0
        upcoming_count = 0
        finished_count = 0
        
        for i, match in enumerate(matches, 1):
            status = match['status']
            
            # Определяем иконку статуса
            if "LIVE" in status:
                status_icon = "🔴"
                live_count += 1
            elif status == "СКОРО":
                status_icon = "🟡"
                upcoming_count += 1
            elif status == "ЗАВЕРШЕН":
                status_icon = "⚫"
                finished_count += 1
            else:
                status_icon = "⚪"
            
            print(f"\n{i}. {status_icon} {match['home_team']} vs {match['away_team']}")
            print(f"   ⚽ Счет: {match['home_score']} - {match['away_score']}")
            print(f"   📊 Статус: {status}")
            print(f"   🏆 {match['league']} ({match['country']})")
            print(f"   🕐 {match['datetime']}")
            
            # Показываем события
            if match['events']:
                print(f"   📋 События:")
                for event in match['events'][:3]:  # Показываем 3 последних события
                    if isinstance(event, dict):
                        event_type = event.get('type', 'событие')
                        minute = event.get('minute', event.get('time', '?'))
                        player = event.get('player', '')
                        print(f"      {minute}' {event_type} {player}")
            
            print("-" * 50)
        
        # Статистика
        print(f"\n📊 СТАТИСТИКА:")
        print(f"   🔴 Live: {live_count}")
        print(f"   🟡 Скоро: {upcoming_count}")
        print(f"   ⚫ Завершено: {finished_count}")
        print(f"   📈 Всего: {len(matches)}")
    
    def start_live_monitoring(self):
        """Запускаем мониторинг live матчей"""
        print("🚀 ЗАПУСК LIVE МОНИТОРИНГА")
        print("Нажмите Ctrl+C для остановки")
        print("=" * 40)
        
        self.running = True
        update_interval = 30  # Обновляем каждые 30 секунд
        
        try:
            while self.running:
                # Собираем новые данные (каждые 5 минут)
                if int(time.time()) % 300 == 0:  # Каждые 5 минут
                    print("🔄 Обновление данных...")
                    self.collect_live_matches()
                
                # Получаем и показываем матчи
                matches = self.get_live_matches_from_db()
                self.display_live_matches(matches)
                
                # Ждем до следующего обновления
                time.sleep(update_interval)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  Мониторинг остановлен пользователем")
            self.running = False
        except Exception as e:
            print(f"\n❌ Ошибка мониторинга: {e}")
            self.running = False

def create_sample_live_data():
    """Создаем примеры live данных для демонстрации"""
    print("🧪 СОЗДАНИЕ ДЕМОНСТРАЦИОННЫХ LIVE ДАННЫХ")
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        scraper = MatchDataScraper(db_path='database/live_matches.db')
        db = scraper.get_database()
        cursor = db.get_cursor()
        
        # Создаем примеры live матчей
        now = datetime.now()
        sample_matches = [
            {
                'flashscore_id': 'LIVE_001',
                'sport_id': 1,
                'country': 'Kazakhstan',
                'league': 'Premier League',
                'season': 2024,
                'datetime': now.strftime('%Y-%m-%d %H:%M:%S'),
                'home_team': 'FC Astana',
                'away_team': 'FC Kairat',
                'home_goals': 1,
                'away_goals': 0,
                'result': None,
                'additional_data': json.dumps({
                    'status': 'live',
                    'minute': 67,
                    'events': [
                        {'minute': 23, 'type': 'goal', 'player': 'Мусаев', 'team': 'home'},
                        {'minute': 45, 'type': 'yellow_card', 'player': 'Жанов', 'team': 'away'},
                        {'minute': 65, 'type': 'substitution', 'player_out': 'Петров', 'player_in': 'Иванов', 'team': 'home'}
                    ]
                })
            },
            {
                'flashscore_id': 'LIVE_002',
                'sport_id': 1,
                'country': 'Kazakhstan',
                'league': 'Premier League',
                'season': 2024,
                'datetime': (now.replace(hour=now.hour+1)).strftime('%Y-%m-%d %H:%M:%S'),
                'home_team': 'FC Shakhter',
                'away_team': 'FC Tobol',
                'home_goals': 0,
                'away_goals': 0,
                'result': None,
                'additional_data': json.dumps({
                    'status': 'upcoming',
                    'events': []
                })
            }
        ]
        
        for match in sample_matches:
            cursor.execute("""
                INSERT OR REPLACE INTO match_data 
                (flashscore_id, sport_id, country, league, season, datetime, 
                 home_team, away_team, home_goals, away_goals, result, additional_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                match['flashscore_id'], match['sport_id'], match['country'],
                match['league'], match['season'], match['datetime'],
                match['home_team'], match['away_team'], match['home_goals'],
                match['away_goals'], match['result'], match['additional_data']
            ))
        
        cursor.connection.commit()
        db.close()
        
        print(f"✅ Создано {len(sample_matches)} демонстрационных live матчей")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания демо данных: {e}")
        return False

def main():
    """Главная функция live мониторинга"""
    print("🔴 LIVE МАТЧИ - FLASHSCORE SCRAPER")
    print("=" * 50)
    
    monitor = LiveMatchMonitor()
    
    # Сначала пробуем собрать реальные данные
    print("🔄 Сбор актуальных данных...")
    real_data = monitor.collect_live_matches()
    
    # Проверяем есть ли данные
    matches = monitor.get_live_matches_from_db()
    
    if not matches:
        print("❌ Реальные данные не найдены")
        print("🧪 Создаем демонстрационные данные...")
        
        if create_sample_live_data():
            matches = monitor.get_live_matches_from_db()
    
    if matches:
        print(f"✅ Найдено {len(matches)} матчей")
        print("\n🚀 Запуск live мониторинга...")
        print("Обновление каждые 30 секунд")
        print("Нажмите Ctrl+C для остановки\n")
        
        time.sleep(2)  # Небольшая пауза
        
        # Запускаем мониторинг
        monitor.start_live_monitoring()
    else:
        print("❌ Не удалось получить данные о матчах")
        print("\n💡 Попробуйте:")
        print("   • Проверить подключение к интернету")
        print("   • Запустить в другое время дня")
        print("   • Собрать данные вручную с помощью других скриптов")

if __name__ == "__main__":
    main()
