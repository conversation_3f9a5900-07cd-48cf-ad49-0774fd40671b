# extract_matches.py
import requests
from bs4 import BeautifulSoup
import re
import json

def extract_matches_from_flashscore():
    """Извлекаем записи матчей с FlashScore"""

    url = "https://www.flashscore.com/football/kazakhstan/"

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        print(f"🔄 Получаем данные с: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        print(f"✅ Статус: {response.status_code}")
        print(f"📊 Размер: {len(response.text)} символов")

        if response.status_code == 200:
            # Парсим HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # Ищем различные элементы матчей
            matches_found = []

            # Метод 1: Ищем по классам связанным с матчами
            print(f"\n🔍 ПОИСК ЗАПИСЕЙ МАТЧЕЙ:")

            # Ищем элементы с классами содержащими 'match', 'event', 'game'
            match_selectors = [
                '[class*="match"]',
                '[class*="event"]',
                '[class*="game"]',
                '[class*="fixture"]',
                '.event__match',
                '.match',
                '.event'
            ]

            for selector in match_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"   • {selector}: найдено {len(elements)} элементов")
                    matches_found.extend(elements)

            # Метод 2: Ищем JSON данные в скриптах
            print(f"\n🔍 ПОИСК JSON ДАННЫХ:")
            scripts = soup.find_all('script')
            json_data_found = []
            all_json_objects = []

            for script in scripts:
                if script.string:
                    # Ищем ВСЕ JSON объекты
                    json_objects = re.findall(r'\{[^{}]*\}', script.string)
                    all_json_objects.extend(json_objects)

                    # Ищем JSON с данными матчей
                    if any(keyword in script.string.lower() for keyword in ['match', 'event', 'fixture', 'game']):
                        json_data_found.extend(json_objects)
                        print(f"   • Скрипт с матчами: {len(json_objects)} JSON объектов")

                        # Показываем содержимое скрипта
                        script_preview = script.string[:500] + "..." if len(script.string) > 500 else script.string
                        print(f"     Превью: {script_preview}")

            print(f"   • Всего JSON объектов на странице: {len(all_json_objects)}")

            # Анализируем все JSON объекты
            interesting_json = []
            for json_str in all_json_objects:
                if any(keyword in json_str.lower() for keyword in ['match', 'event', 'fixture', 'game', 'team', 'score']):
                    interesting_json.append(json_str)

            print(f"   • Интересных JSON объектов: {len(interesting_json)}")

            # Метод 3: Ищем таблицы с результатами
            print(f"\n🔍 ПОИСК ТАБЛИЦ:")
            tables = soup.find_all('table')
            if tables:
                print(f"   • Найдено {len(tables)} таблиц")

            # Метод 4: Ищем списки
            lists = soup.find_all(['ul', 'ol'])
            match_lists = [lst for lst in lists if any(keyword in str(lst).lower() for keyword in ['match', 'event', 'fixture'])]
            if match_lists:
                print(f"   • Найдено {len(match_lists)} списков с матчами")

            # Выводим найденные данные
            print(f"\n📋 РЕЗУЛЬТАТЫ ПОИСКА:")
            print(f"   • HTML элементы матчей: {len(set(matches_found))}")
            print(f"   • JSON данные: {len(json_data_found)}")
            print(f"   • Таблицы: {len(tables)}")
            print(f"   • Списки матчей: {len(match_lists)}")

            # Показываем примеры найденных данных
            if matches_found:
                print(f"\n📄 ПРИМЕРЫ HTML ЭЛЕМЕНТОВ:")
                for i, match in enumerate(list(set(matches_found))[:3], 1):
                    print(f"   {i}. Тег: {match.name}")
                    print(f"      Классы: {match.get('class', [])}")
                    print(f"      Текст: {match.get_text()[:100]}...")
                    print()

            if interesting_json:
                print(f"\n📄 ИНТЕРЕСНЫЕ JSON ДАННЫЕ:")
                for i, json_str in enumerate(interesting_json[:5], 1):
                    print(f"   {i}. {json_str[:200]}...")

                    # Пробуем распарсить JSON
                    try:
                        parsed = json.loads(json_str)
                        print(f"      Ключи: {list(parsed.keys())}")
                    except:
                        print(f"      (не валидный JSON)")
                    print()

            # Пробуем найти конкретные команды Казахстана
            print(f"\n🇰🇿 ПОИСК КАЗАХСТАНСКИХ КОМАНД:")
            kz_teams = ['Astana', 'Kairat', 'Shakhter', 'Tobol', 'Ordabasy', 'Aktobe']

            for team in kz_teams:
                if team.lower() in response.text.lower():
                    print(f"   ✅ Найдена команда: {team}")

                    # Ищем контекст вокруг названия команды
                    pattern = rf'.{{0,50}}{re.escape(team)}.{{0,50}}'
                    contexts = re.findall(pattern, response.text, re.IGNORECASE)
                    if contexts:
                        print(f"      Контекст: {contexts[0][:100]}...")

            return {
                'html_elements': len(set(matches_found)),
                'json_data': len(json_data_found),
                'interesting_json': len(interesting_json),
                'all_json': len(all_json_objects),
                'tables': len(tables),
                'match_lists': len(match_lists),
                'page_size': len(response.text)
            }

        else:
            print(f"❌ Ошибка HTTP: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None

if __name__ == "__main__":
    result = extract_matches_from_flashscore()
    if result:
        print(f"\n🎯 ИТОГО НАЙДЕНО:")
        for key, value in result.items():
            print(f"   • {key}: {value}")
    else:
        print(f"\n❌ Не удалось извлечь данные")