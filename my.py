# manual_check.py
import requests
from bs4 import BeautifulSoup

def check_flashscore_urls():
    """Проверка доступности URL Flashscore"""
    
    test_urls = [
        "https://www.flashscore.com/football/england/premier-league/",
        "https://www.flashscore.com/football/",
        "https://www.flashscore.com/football/england/premier-league/results/",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for url in test_urls:
        try:
            print(f"Проверка: {url}")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"  Статус: {response.status_code}")
            print(f"  Размер: {len(response.text)} символов")
            
            if response.status_code == 200:
                # Проверим, есть ли признаки матчей на странице
                if 'match' in response.text.lower() or 'football' in response.text.lower():
                    print("  ✓ Страница содержит футбольные данные")
                else:
                    print("  ! Страница может не содержать ожидаемые данные")
            print()
            
        except Exception as e:
            print(f"  ✗ Ошибка: {e}")
            print()

if __name__ == "__main__":
    check_flashscore_urls()