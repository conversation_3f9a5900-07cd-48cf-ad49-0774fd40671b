#!/usr/bin/env python3
"""
РЕАЛЬНЫЙ СБОР АКТУАЛЬНЫХ ДАННЫХ С FLASHSCORE
Собираем настоящие данные о матчах и пенальти прямо сейчас
"""

import os
import sqlite3
import json
import time
from datetime import datetime, timedelta

def setup_real_scraping():
    """Настройка для реального сбора данных"""
    print("🔥 НАСТРОЙКА РЕАЛЬНОГО СБОРА ДАННЫХ")
    print("=" * 40)
    
    os.makedirs('database', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Создаем конфигурацию с РЕАЛЬНЫМИ URL FlashScore
    real_config = """
sport_ids:
  1:  # Football
    leagues:
      - name: "Kazakhstan Premier League"
        country: "Kazakhstan"
        url: "https://www.flashscore.com/football/kazakhstan/premier-league/"
        seasons: [2024]
        url_pattern: "https://www.flashscore.com/football/kazakhstan/premier-league/"
"""
    
    with open('config/real_flashscore.yaml', 'w') as f:
        f.write(real_config)
    
    print("✅ Конфигурация для реального сбора создана")

def collect_real_match_data():
    """Собираем РЕАЛЬНЫЕ данные матчей"""
    print("\n🚀 СБОР РЕАЛЬНЫХ ДАННЫХ МАТЧЕЙ")
    print("=" * 35)
    
    try:
        from flashscore_scraper import MatchDataScraper, MatchIDScraper
        
        # Сначала собираем ID матчей
        print("1️⃣ Сбор ID матчей...")
        id_scraper = MatchIDScraper(
            config_path='config/real_flashscore.yaml',
            db_path='database/real_match_ids.db'
        )
        
        print("⏳ Запускаем сбор ID матчей (это займет время)...")
        id_result = id_scraper.scrape()
        print(f"✅ ID матчей: {id_result}")
        
        # Теперь собираем детальные данные
        print("\n2️⃣ Сбор детальных данных матчей...")
        data_scraper = MatchDataScraper(db_path='database/real_match_data.db')
        
        # Собираем данные за последние 30 дней
        print("⏳ Собираем данные за последние 30 дней...")
        data_result = data_scraper.update(days=30, batch_size=50, headless=True)
        print(f"✅ Данные матчей: {data_result}")
        
        # Также пробуем прямой scrape
        print("\n3️⃣ Прямой сбор данных...")
        scrape_result = data_scraper.scrape(batch_size=100, headless=True)
        print(f"✅ Прямой сбор: {scrape_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при сборе данных: {e}")
        return False

def check_real_data():
    """Проверяем собранные РЕАЛЬНЫЕ данные"""
    print("\n🔍 ПРОВЕРКА РЕАЛЬНЫХ ДАННЫХ")
    print("=" * 30)
    
    databases = [
        'database/real_match_ids.db',
        'database/real_match_data.db'
    ]
    
    total_matches = 0
    matches_with_data = 0
    
    for db_path in databases:
        if not os.path.exists(db_path):
            print(f"❌ {db_path} не найдена")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            print(f"\n📊 База данных: {os.path.basename(db_path)}")
            
            # Проверяем таблицы
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"   • {table_name}: {count} записей")
                
                if table_name == 'match_data':
                    total_matches += count
                    
                    # Проверяем записи с additional_data
                    cursor.execute("SELECT COUNT(*) FROM match_data WHERE additional_data IS NOT NULL AND additional_data != '';")
                    with_data = cursor.fetchone()[0]
                    matches_with_data += with_data
                    print(f"     └─ С дополнительными данными: {with_data}")
                    
                    # Показываем примеры
                    if count > 0:
                        cursor.execute("SELECT home_team, away_team, datetime, additional_data FROM match_data LIMIT 3;")
                        examples = cursor.fetchall()
                        print(f"     └─ Примеры:")
                        for i, (home, away, dt, data) in enumerate(examples, 1):
                            has_data = "✅" if data else "❌"
                            print(f"        {i}. {home} vs {away} ({dt}) {has_data}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Ошибка при проверке {db_path}: {e}")
    
    print(f"\n📈 ИТОГО:")
    print(f"   • Всего матчей: {total_matches}")
    print(f"   • С дополнительными данными: {matches_with_data}")
    
    return matches_with_data > 0

def search_real_penalties():
    """Ищем РЕАЛЬНЫЕ пенальти в собранных данных"""
    print("\n🥅 ПОИСК РЕАЛЬНЫХ ПЕНАЛЬТИ")
    print("=" * 30)
    
    databases = [
        'database/real_match_data.db',
        'database/real_match_ids.db'
    ]
    
    all_penalties = []
    
    for db_path in databases:
        if not os.path.exists(db_path):
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Ищем матчи с дополнительными данными
            cursor.execute("""
                SELECT flashscore_id, country, league, datetime, home_team, away_team, 
                       home_goals, away_goals, additional_data
                FROM match_data 
                WHERE additional_data IS NOT NULL AND additional_data != ''
                ORDER BY datetime DESC
            """)
            
            matches = cursor.fetchall()
            print(f"📊 Матчей с данными в {os.path.basename(db_path)}: {len(matches)}")
            
            for match in matches:
                (match_id, country, league, dt, home, away, h_goals, a_goals, add_data) = match
                
                # Ищем пенальти в дополнительных данных
                penalty_found = False
                penalty_details = []
                
                if add_data:
                    data_str = str(add_data).lower()
                    
                    # Ключевые слова для пенальти
                    penalty_keywords = ['penalty', 'пенальти', 'pen', 'pk', 'shootout']
                    
                    found_keywords = [kw for kw in penalty_keywords if kw in data_str]
                    
                    if found_keywords:
                        penalty_found = True
                        penalty_details = found_keywords
                        
                        penalty_match = {
                            'id': match_id,
                            'home': home,
                            'away': away,
                            'date': dt,
                            'league': league,
                            'score': f"{h_goals}-{a_goals}" if h_goals is not None else "N/A",
                            'keywords': found_keywords,
                            'data': add_data[:200] + "..." if len(add_data) > 200 else add_data
                        }
                        all_penalties.append(penalty_match)
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Ошибка поиска в {db_path}: {e}")
    
    # Показываем найденные пенальти
    if all_penalties:
        print(f"\n🎯 НАЙДЕНО МАТЧЕЙ С ПЕНАЛЬТИ: {len(all_penalties)}")
        
        for i, match in enumerate(all_penalties[:10], 1):  # Показываем первые 10
            print(f"\n{i}. ⚽ {match['home']} vs {match['away']}")
            print(f"   📅 {match['date']}")
            print(f"   🏆 {match['league']}")
            print(f"   ⚽ Счет: {match['score']}")
            print(f"   🔍 Найдено: {', '.join(match['keywords'])}")
            print(f"   📝 Данные: {match['data']}")
            print("-" * 50)
    else:
        print("❌ Пенальти в реальных данных не найдены")
    
    return len(all_penalties)

def try_alternative_scraping():
    """Пробуем альтернативные методы сбора"""
    print("\n🔄 АЛЬТЕРНАТИВНЫЕ МЕТОДЫ СБОРА")
    print("=" * 35)
    
    try:
        from flashscore_scraper import Football, Handball, Volleyball
        
        # Пробуем класс Football
        print("1️⃣ Пробуем класс Football...")
        try:
            football = Football()
            
            # Пробуем разные методы
            methods = ['load_matches', 'load_fixtures', 'load_odds']
            for method_name in methods:
                if hasattr(football, method_name):
                    print(f"   🔄 {method_name}...")
                    try:
                        method = getattr(football, method_name)
                        result = method()
                        print(f"   ✅ {method_name}: {result}")
                    except Exception as e:
                        print(f"   ❌ {method_name}: {e}")
        except Exception as e:
            print(f"   ❌ Football класс: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка альтернативных методов: {e}")
        return False

def main():
    """Главная функция сбора РЕАЛЬНЫХ данных"""
    print("🔥 СБОР РЕАЛЬНЫХ АКТУАЛЬНЫХ ДАННЫХ")
    print("FlashScore Scraper - Казахстан")
    print("=" * 50)
    print("⚠️  ВНИМАНИЕ: Реальные запросы к FlashScore!")
    print("Может занять несколько минут...")
    print("=" * 50)
    
    # 1. Настройка
    setup_real_scraping()
    
    # 2. Сбор реальных данных
    print(f"\n🚀 Начинаем сбор в {datetime.now().strftime('%H:%M:%S')}")
    scraping_success = collect_real_match_data()
    
    # 3. Проверка собранных данных
    data_found = check_real_data()
    
    # 4. Поиск пенальти в реальных данных
    penalties_found = search_real_penalties()
    
    # 5. Альтернативные методы если основные не сработали
    if not data_found:
        print("\n🔄 Основные методы не дали результата, пробуем альтернативные...")
        try_alternative_scraping()
    
    # Итоги
    print("\n" + "=" * 50)
    print("📊 ИТОГИ СБОРА РЕАЛЬНЫХ ДАННЫХ:")
    print(f"   • Сбор данных: {'✅' if scraping_success else '❌'}")
    print(f"   • Найдены данные: {'✅' if data_found else '❌'}")
    print(f"   • Найдены пенальти: {penalties_found}")
    
    if penalties_found > 0:
        print(f"\n🎉 УСПЕХ! Найдено {penalties_found} матчей с пенальти!")
    elif data_found:
        print(f"\n💡 Данные собраны, но пенальти не найдены")
        print("   Возможно нужно собрать больше матчей или за другой период")
    else:
        print(f"\n❌ Данные не собраны")
        print("💡 Возможные причины:")
        print("   • Нет подключения к интернету")
        print("   • FlashScore блокирует запросы")
        print("   • Нужен другой подход к конфигурации")
        print("   • Требуется установка Chrome/Firefox")
    
    print(f"\n📁 Проверьте базы данных:")
    print(f"   • database/real_match_ids.db")
    print(f"   • database/real_match_data.db")

if __name__ == "__main__":
    main()
