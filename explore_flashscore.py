#!/usr/bin/env python3
"""
Скрипт для изучения возможностей библиотеки flashscore-scraper
"""

import sys
import inspect
from pprint import pprint

def explore_flashscore_library():
    """Изучаем структуру и возможности библиотеки flashscore-scraper"""
    
    print("=== Изучение библиотеки flashscore-scraper ===\n")
    
    try:
        # Импортируем основную библиотеку
        import flashscore_scraper
        print("✅ Библиотека flashscore-scraper успешно импортирована")
        print(f"Версия: {getattr(flashscore_scraper, '__version__', 'неизвестна')}")
        print(f"Путь: {flashscore_scraper.__file__}")
        print()
        
        # Изучаем доступные модули и классы
        print("=== Доступные атрибуты в flashscore_scraper ===")
        attributes = [attr for attr in dir(flashscore_scraper) if not attr.startswith('_')]
        for attr in attributes:
            obj = getattr(flashscore_scraper, attr)
            if inspect.isclass(obj):
                print(f"📦 Класс: {attr}")
                # Показываем методы класса
                methods = [method for method in dir(obj) if not method.startswith('_') and callable(getattr(obj, method))]
                for method in methods[:5]:  # Показываем первые 5 методов
                    print(f"   └── {method}()")
                if len(methods) > 5:
                    print(f"   └── ... и еще {len(methods) - 5} методов")
            elif inspect.isfunction(obj):
                print(f"🔧 Функция: {attr}")
            elif inspect.ismodule(obj):
                print(f"📁 Модуль: {attr}")
            else:
                print(f"📄 Атрибут: {attr} ({type(obj).__name__})")
        print()
        
        # Попробуем найти основные классы для скрапинга
        potential_scrapers = []
        for attr in attributes:
            obj = getattr(flashscore_scraper, attr)
            if inspect.isclass(obj) and ('scraper' in attr.lower() or 'client' in attr.lower() or 'api' in attr.lower()):
                potential_scrapers.append((attr, obj))
        
        if potential_scrapers:
            print("=== Найденные классы для скрапинга ===")
            for name, cls in potential_scrapers:
                print(f"🎯 {name}")
                # Показываем конструктор
                try:
                    sig = inspect.signature(cls.__init__)
                    print(f"   Конструктор: {name}{sig}")
                except:
                    print(f"   Конструктор: {name}(...)")
                
                # Показываем документацию класса
                if cls.__doc__:
                    doc_lines = cls.__doc__.strip().split('\n')
                    print(f"   Описание: {doc_lines[0]}")
                print()
        
        # Попробуем создать экземпляр основного класса
        print("=== Попытка создания экземпляра скрапера ===")
        try:
            # Ищем основной класс скрапера
            main_scraper_class = None
            for attr in attributes:
                obj = getattr(flashscore_scraper, attr)
                if inspect.isclass(obj) and 'scraper' in attr.lower():
                    main_scraper_class = obj
                    break
            
            if main_scraper_class:
                print(f"Найден основной класс: {main_scraper_class.__name__}")
                # Попробуем создать экземпляр с минимальными параметрами
                scraper = main_scraper_class()
                print("✅ Экземпляр скрапера создан успешно")
                
                # Изучаем методы экземпляра
                methods = [method for method in dir(scraper) if not method.startswith('_') and callable(getattr(scraper, method))]
                print(f"Доступные методы ({len(methods)}):")
                for method in methods:
                    try:
                        sig = inspect.signature(getattr(scraper, method))
                        print(f"   • {method}{sig}")
                    except:
                        print(f"   • {method}(...)")
                
            else:
                print("❌ Основной класс скрапера не найден")
                
        except Exception as e:
            print(f"❌ Ошибка при создании экземпляра: {e}")
        
    except ImportError as e:
        print(f"❌ Ошибка импорта: {e}")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False
    
    return True

def explore_data_types():
    """Изучаем типы данных, которые можно собирать"""
    print("\n=== Типы данных, доступные для сбора ===")
    
    try:
        import flashscore_scraper
        
        # Ищем модели данных или константы
        for attr in dir(flashscore_scraper):
            if not attr.startswith('_'):
                obj = getattr(flashscore_scraper, attr)
                if inspect.isclass(obj) and ('model' in attr.lower() or 'data' in attr.lower()):
                    print(f"📊 Модель данных: {attr}")
                    if obj.__doc__:
                        print(f"   {obj.__doc__.strip().split('.')[0]}")
                elif isinstance(obj, (list, tuple, dict)) and len(str(obj)) < 200:
                    print(f"📋 Константа: {attr} = {obj}")
        
    except Exception as e:
        print(f"❌ Ошибка при изучении типов данных: {e}")

if __name__ == "__main__":
    success = explore_flashscore_library()
    if success:
        explore_data_types()
        print("\n=== Заключение ===")
        print("Библиотека flashscore-scraper установлена и готова к использованию!")
        print("Для получения более подробной информации о возможностях библиотеки")
        print("рекомендуется изучить официальную документацию или примеры кода.")
    else:
        print("\n❌ Не удалось полностью изучить библиотеку")
        sys.exit(1)
