#!/usr/bin/env python3
"""
РЕАЛЬНЫЙ СКРАПЕР ПО ДОКУМЕНТАЦИИ
Используем FlexibleScraper как в официальной документации
"""

def test_flexible_scraper():
    """Тестируем FlexibleScraper по документации"""
    print("🚀 ТЕСТИРУЕМ FLEXIBLESCRAPER")
    print("=" * 35)
    
    try:
        from flashscore_scraper import FlexibleScraper
        print("✅ FlexibleScraper импортирован")
        
        # Создаем скрапер без фильтров
        scraper = FlexibleScraper()
        print("✅ FlexibleScraper создан")
        
        # Получаем доступные фильтры
        print("\n🔍 Получаем доступные фильтры...")
        available_filters = scraper.get_available_filters()
        print("✅ Фильтры получены:")
        print(available_filters)
        
        return True, scraper
        
    except ImportError as e:
        print(f"❌ FlexibleScraper не найден: {e}")
        return False, None
    except Exception as e:
        print(f"❌ Ошибка FlexibleScraper: {e}")
        return False, None

def test_data_loaders():
    """Тестируем data loaders по документации"""
    print("\n⚽ ТЕСТИРУЕМ DATA LOADERS")
    print("=" * 30)
    
    # Тестируем Football loader
    try:
        from flashscore_scraper.data_loaders import Football
        print("✅ Football loader импортирован")
        
        loader = Football()
        print("✅ Football loader создан")
        
        # Пробуем загрузить матчи
        try:
            df = loader.load_matches(
                league="Premier League",
                seasons=["2023/2024"],
                include_additional_data=True
            )
            print(f"✅ Football матчи загружены: {len(df)} записей")
            if len(df) > 0:
                print(f"   Колонки: {list(df.columns)}")
        except Exception as e:
            print(f"❌ Ошибка загрузки Football матчей: {e}")
        
    except ImportError as e:
        print(f"❌ Football loader не найден: {e}")
    except Exception as e:
        print(f"❌ Ошибка Football loader: {e}")
    
    # Тестируем Handball loader
    try:
        from flashscore_scraper.data_loaders import Handball
        print("✅ Handball loader импортирован")
        
        loader = Handball()
        print("✅ Handball loader создан")
        
        try:
            df = loader.load_matches(
                league="Herre Handbold Ligaen",
                include_additional_data=True
            )
            print(f"✅ Handball матчи загружены: {len(df)} записей")
        except Exception as e:
            print(f"❌ Ошибка загрузки Handball матчей: {e}")
        
    except ImportError as e:
        print(f"❌ Handball loader не найден: {e}")
    except Exception as e:
        print(f"❌ Ошибка Handball loader: {e}")

def test_scraping_with_filters():
    """Тестируем скрапинг с фильтрами"""
    print("\n🎯 ТЕСТИРУЕМ СКРАПИНГ С ФИЛЬТРАМИ")
    print("=" * 40)
    
    try:
        from flashscore_scraper import FlexibleScraper
        
        # Фильтры для футбола (как в документации)
        filters = {
            "sports": ["football"],
            "countries": ["Kazakhstan"]
        }
        
        print(f"🔧 Создаем скрапер с фильтрами: {filters}")
        scraper = FlexibleScraper(filters=filters)
        print("✅ Скрапер с фильтрами создан")
        
        # Запускаем скрапинг
        print("\n🚀 Запускаем скрапинг...")
        try:
            results = scraper.scrape(headless=True, batch_size=10, scrape_odds=False)
            print(f"✅ Скрапинг завершен: {results}")
            return results
        except Exception as e:
            print(f"❌ Ошибка скрапинга: {e}")
            return None
        
    except Exception as e:
        print(f"❌ Ошибка создания скрапера с фильтрами: {e}")
        return None

def create_config_file():
    """Создаем конфигурационный файл как в документации"""
    print("\n📝 СОЗДАЕМ КОНФИГУРАЦИОННЫЙ ФАЙЛ")
    print("=" * 40)
    
    import os
    os.makedirs('config', exist_ok=True)
    
    config_content = """
sports:
  football:
    leagues:
      - name: "Kazakhstan Premier League"
        country: "Kazakhstan"
        url: "https://www.flashscore.com/football/kazakhstan/premier-league/"
        seasons: [2024, 2023]
      - name: "Kazakhstan First Division"
        country: "Kazakhstan"
        url: "https://www.flashscore.com/football/kazakhstan/first-division/"
        seasons: [2024, 2023]
  handball:
    leagues:
      - name: "Kazakhstan Handball League"
        country: "Kazakhstan"
        url: "https://www.flashscore.com/handball/kazakhstan/"
        seasons: [2024]
"""
    
    with open('config/flashscore_urls.yaml', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Конфигурационный файл создан: config/flashscore_urls.yaml")

def test_individual_scrapers():
    """Тестируем отдельные скраперы"""
    print("\n🕷️ ТЕСТИРУЕМ ОТДЕЛЬНЫЕ СКРАПЕРЫ")
    print("=" * 35)
    
    # MatchIDScraper
    try:
        from flashscore_scraper import MatchIDScraper
        
        scraper = MatchIDScraper(
            config_path='config/flashscore_urls.yaml',
            db_path='database/real_test.db'
        )
        print("✅ MatchIDScraper создан")
        
        # Пробуем scrape
        try:
            result = scraper.scrape()
            print(f"✅ MatchIDScraper.scrape(): {result}")
        except Exception as e:
            print(f"❌ MatchIDScraper.scrape(): {e}")
        
    except Exception as e:
        print(f"❌ MatchIDScraper: {e}")
    
    # MatchDataScraper
    try:
        from flashscore_scraper import MatchDataScraper
        
        scraper = MatchDataScraper(db_path='database/real_test.db')
        print("✅ MatchDataScraper создан")
        
        try:
            result = scraper.scrape(headless=True, batch_size=5)
            print(f"✅ MatchDataScraper.scrape(): {result}")
        except Exception as e:
            print(f"❌ MatchDataScraper.scrape(): {e}")
        
    except Exception as e:
        print(f"❌ MatchDataScraper: {e}")
    
    # OddsDataScraper
    try:
        from flashscore_scraper import OddsDataScraper
        
        scraper = OddsDataScraper(db_path='database/real_test.db')
        print("✅ OddsDataScraper создан")
        
        try:
            result = scraper.scrape()
            print(f"✅ OddsDataScraper.scrape(): {result}")
        except Exception as e:
            print(f"❌ OddsDataScraper.scrape(): {e}")
        
    except Exception as e:
        print(f"❌ OddsDataScraper: {e}")

def check_database_after_scraping():
    """Проверяем базу данных после скрапинга"""
    print("\n🔍 ПРОВЕРЯЕМ БАЗУ ДАННЫХ")
    print("=" * 25)
    
    import sqlite3
    import os
    
    db_path = 'database/real_test.db'
    
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"📊 Найдено таблиц: {len(tables)}")
            
            total_records = 0
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                total_records += count
                print(f"   • {table_name}: {count} записей")
                
                # Показываем примеры данных
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 2;")
                    rows = cursor.fetchall()
                    print(f"     Примеры:")
                    for i, row in enumerate(rows, 1):
                        print(f"     {i}. {str(row)[:100]}...")
            
            print(f"\n📈 ВСЕГО ЗАПИСЕЙ: {total_records}")
            
            # Специально ищем данные с additional_data
            try:
                cursor.execute("SELECT COUNT(*) FROM match_data WHERE additional_data IS NOT NULL AND additional_data != '';")
                with_data = cursor.fetchone()[0]
                print(f"🎯 Записей с additional_data: {with_data}")
                
                if with_data > 0:
                    cursor.execute("SELECT home_team, away_team, additional_data FROM match_data WHERE additional_data IS NOT NULL LIMIT 1;")
                    sample = cursor.fetchone()
                    if sample:
                        print(f"📋 Пример additional_data:")
                        print(f"   {sample[0]} vs {sample[1]}")
                        print(f"   Данные: {sample[2][:200]}...")
            except:
                pass
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Ошибка проверки БД: {e}")
    else:
        print("❌ База данных не найдена")

def main():
    """Главная функция тестирования по документации"""
    print("📚 ТЕСТИРОВАНИЕ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ")
    print("=" * 60)
    print("Источник: https://pypi.org/project/flashscore-scraper/")
    print("=" * 60)
    
    # 1. Создаем конфигурационный файл
    create_config_file()
    
    # 2. Тестируем FlexibleScraper
    flexible_success, scraper = test_flexible_scraper()
    
    # 3. Тестируем data loaders
    test_data_loaders()
    
    # 4. Тестируем скрапинг с фильтрами
    if flexible_success:
        scraping_results = test_scraping_with_filters()
    
    # 5. Тестируем отдельные скраперы
    test_individual_scrapers()
    
    # 6. Проверяем результаты в базе данных
    check_database_after_scraping()
    
    print("\n" + "=" * 60)
    print("🎯 ИТОГИ ТЕСТИРОВАНИЯ ПО ДОКУМЕНТАЦИИ:")
    print(f"   • FlexibleScraper: {'✅' if flexible_success else '❌'}")
    print("   • Конфигурация создана: ✅")
    print("   • Отдельные скраперы протестированы: ✅")
    
    print("\n📋 СЛЕДУЮЩИЕ ШАГИ:")
    print("   1. Проверить результаты в database/real_test.db")
    print("   2. Если данные есть - искать пенальти в additional_data")
    print("   3. Если данных нет - проверить интернет и настройки браузера")

if __name__ == "__main__":
    main()
