#!/usr/bin/env python3
"""
Тестируем пример Handball из документации
"""

print("🤾 ТЕСТИРУЕМ HANDBALL ПРИМЕР ИЗ ДОКУМЕНТАЦИИ")
print("=" * 50)

try:
    from flashscore_scraper.data_loaders import Handball
    print("✅ Handball импортирован")
    
    loader = Handball()
    print("✅ Handball loader создан")
    
    print("\n🔄 Загружаем матчи...")
    df = loader.load_matches(
        league="Herre Handbold Ligaen",
        include_additional_data=True
    )
    
    print(f"✅ Матчи загружены: {len(df)} записей")
    
    if len(df) > 0:
        print("\n📊 РЕЗУЛЬТАТ:")
        print("Колонки:", list(df.columns))
        print("\nПервые записи:")
        print(df.head())
    else:
        print("\n⚠️  Данных нет, но метод работает!")
        
except Exception as e:
    print(f"❌ Ошибка: {e}")
    print(f"   Тип ошибки: {type(e).__name__}")

print("\n" + "=" * 50)
print("🔄 ТЕСТИРУЕМ FOOTBALL")

try:
    from flashscore_scraper.data_loaders import Football
    print("✅ Football импортирован")
    
    loader = Football()
    print("✅ Football loader создан")
    
    print("\n🔄 Загружаем футбольные матчи...")
    df = loader.load_matches(
        league="Premier League",
        seasons=["2023/2024"],
        include_additional_data=True
    )
    
    print(f"✅ Футбольные матчи: {len(df)} записей")
    
    if len(df) > 0:
        print("\n📊 РЕЗУЛЬТАТ FOOTBALL:")
        print("Колонки:", list(df.columns))
        print("\nПервые записи:")
        print(df.head())
    else:
        print("\n⚠️  Футбольных данных нет!")
        
except Exception as e:
    print(f"❌ Football ошибка: {e}")

print("\n" + "=" * 50)
print("🏐 ТЕСТИРУЕМ VOLLEYBALL")

try:
    from flashscore_scraper.data_loaders import Volleyball
    print("✅ Volleyball импортирован")
    
    loader = Volleyball()
    print("✅ Volleyball loader создан")
    
    print("\n🔄 Загружаем волейбольные матчи...")
    df = loader.load_matches(
        league="PlusLiga",
        include_additional_data=True
    )
    
    print(f"✅ Волейбольные матчи: {len(df)} записей")
    
    if len(df) > 0:
        print("\n📊 РЕЗУЛЬТАТ VOLLEYBALL:")
        print("Колонки:", list(df.columns))
        print("\nПервые записи:")
        print(df.head())
    else:
        print("\n⚠️  Волейбольных данных нет!")
        
except Exception as e:
    print(f"❌ Volleyball ошибка: {e}")

print("\n🎯 ИТОГ: Посмотрим что дают data loaders")
