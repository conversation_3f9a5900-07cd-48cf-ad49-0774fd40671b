#!/usr/bin/env python3
"""
Тестируем правильные импорты по документации
"""

def test_data_loaders_import():
    """Тестируем импорт data_loaders как в документации"""
    print("🔍 ТЕСТИРУЕМ ИМПОРТ DATA_LOADERS")
    print("=" * 40)
    
    # Пробуем импорт как в документации
    try:
        from flashscore_scraper.data_loaders import Football
        print("✅ from flashscore_scraper.data_loaders import Football - РАБОТАЕТ!")
        
        loader = Football()
        print("✅ Football loader создан")
        
        # Пробуем load_matches
        try:
            df = loader.load_matches(
                league="Premier League",
                seasons=["2023/2024"],
                include_additional_data=True
            )
            print(f"✅ load_matches работает: {len(df)} записей")
            if len(df) > 0:
                print(f"   Колонки: {list(df.columns)}")
            else:
                print("   ⚠️  Данных нет, но метод работает")
        except Exception as e:
            print(f"❌ load_matches ошибка: {e}")
        
    except ImportError as e:
        print(f"❌ Импорт data_loaders.Football не работает: {e}")
        
        # Пробуем альтернативный импорт
        try:
            from flashscore_scraper import Football
            print("✅ from flashscore_scraper import Football - РАБОТАЕТ!")
            
            loader = Football()
            print("✅ Football loader создан (альтернативный импорт)")
            
        except Exception as e2:
            print(f"❌ Альтернативный импорт тоже не работает: {e2}")
    
    except Exception as e:
        print(f"❌ Другая ошибка Football: {e}")

def test_handball_volleyball():
    """Тестируем Handball и Volleyball"""
    print("\n🤾 ТЕСТИРУЕМ HANDBALL И VOLLEYBALL")
    print("=" * 40)
    
    # Handball
    try:
        from flashscore_scraper.data_loaders import Handball
        print("✅ Handball импорт работает")
        
        loader = Handball()
        print("✅ Handball loader создан")
        
        try:
            df = loader.load_matches(
                league="Herre Handbold Ligaen",
                include_additional_data=True
            )
            print(f"✅ Handball load_matches: {len(df)} записей")
        except Exception as e:
            print(f"❌ Handball load_matches: {e}")
            
    except ImportError as e:
        print(f"❌ Handball импорт: {e}")
    except Exception as e:
        print(f"❌ Handball ошибка: {e}")
    
    # Volleyball
    try:
        from flashscore_scraper.data_loaders import Volleyball
        print("✅ Volleyball импорт работает")
        
        loader = Volleyball()
        print("✅ Volleyball loader создан")
        
        try:
            df = loader.load_matches(
                league="PlusLiga",
                include_additional_data=True
            )
            print(f"✅ Volleyball load_matches: {len(df)} записей")
        except Exception as e:
            print(f"❌ Volleyball load_matches: {e}")
            
    except ImportError as e:
        print(f"❌ Volleyball импорт: {e}")
    except Exception as e:
        print(f"❌ Volleyball ошибка: {e}")

def test_flexible_scraper():
    """Тестируем FlexibleScraper"""
    print("\n🔧 ТЕСТИРУЕМ FLEXIBLESCRAPER")
    print("=" * 30)
    
    try:
        from flashscore_scraper import FlexibleScraper
        print("✅ FlexibleScraper импорт работает")
        
        scraper = FlexibleScraper()
        print("✅ FlexibleScraper создан")
        
        # Получаем доступные фильтры
        try:
            available_filters = scraper.get_available_filters()
            print("✅ get_available_filters работает:")
            print(f"   Тип: {type(available_filters)}")
            print(f"   Содержимое: {available_filters}")
        except Exception as e:
            print(f"❌ get_available_filters: {e}")
        
        # Пробуем создать с фильтрами
        try:
            filters = {
                "sports": ["football"],
                "countries": ["Kazakhstan"]
            }
            scraper_with_filters = FlexibleScraper(filters=filters)
            print("✅ FlexibleScraper с фильтрами создан")
            
            # Пробуем scrape
            try:
                results = scraper_with_filters.scrape(headless=True, batch_size=5, scrape_odds=False)
                print(f"✅ scrape работает: {results}")
            except Exception as e:
                print(f"❌ scrape ошибка: {e}")
                
        except Exception as e:
            print(f"❌ FlexibleScraper с фильтрами: {e}")
        
    except ImportError as e:
        print(f"❌ FlexibleScraper импорт: {e}")
    except Exception as e:
        print(f"❌ FlexibleScraper ошибка: {e}")

def explore_data_loaders_module():
    """Исследуем модуль data_loaders"""
    print("\n🔬 ИССЛЕДУЕМ МОДУЛЬ DATA_LOADERS")
    print("=" * 35)
    
    try:
        from flashscore_scraper import data_loaders
        print("✅ data_loaders модуль импортирован")
        
        print("📋 Содержимое data_loaders:")
        for attr in dir(data_loaders):
            if not attr.startswith('_'):
                obj = getattr(data_loaders, attr)
                print(f"   • {attr}: {type(obj)}")
        
    except ImportError as e:
        print(f"❌ data_loaders модуль: {e}")
    except Exception as e:
        print(f"❌ data_loaders ошибка: {e}")

def test_kazakhstan_specific():
    """Тестируем с данными Казахстана"""
    print("\n🇰🇿 ТЕСТИРУЕМ С ДАННЫМИ КАЗАХСТАНА")
    print("=" * 35)
    
    try:
        from flashscore_scraper.data_loaders import Football
        
        loader = Football()
        print("✅ Football loader для Казахстана создан")
        
        # Пробуем загрузить казахстанские матчи
        try:
            df = loader.load_matches(
                league="Kazakhstan Premier League",
                seasons=["2024", "2023"],
                include_additional_data=True
            )
            print(f"✅ Казахстанские матчи: {len(df)} записей")
            
            if len(df) > 0:
                print("📊 Найденные данные:")
                print(f"   Колонки: {list(df.columns)}")
                print(f"   Первые записи:")
                print(df.head())
            else:
                print("⚠️  Казахстанских матчей не найдено")
                
        except Exception as e:
            print(f"❌ Загрузка казахстанских матчей: {e}")
        
    except Exception as e:
        print(f"❌ Ошибка с Казахстаном: {e}")

def main():
    """Главная функция тестирования импортов"""
    print("🧪 ТЕСТИРОВАНИЕ ПРАВИЛЬНЫХ ИМПОРТОВ")
    print("=" * 50)
    print("Проверяем импорты из документации PyPI")
    print("=" * 50)
    
    # 1. Тестируем data_loaders импорт
    test_data_loaders_import()
    
    # 2. Тестируем Handball и Volleyball
    test_handball_volleyball()
    
    # 3. Тестируем FlexibleScraper
    test_flexible_scraper()
    
    # 4. Исследуем модуль data_loaders
    explore_data_loaders_module()
    
    # 5. Тестируем с данными Казахстана
    test_kazakhstan_specific()
    
    print("\n" + "=" * 50)
    print("🎯 ВЫВОДЫ:")
    print("Если импорты работают - проблема в отсутствии данных")
    print("Если импорты не работают - проблема в версии библиотеки")
    print("Нужно проверить какая версия установлена и что она содержит")

if __name__ == "__main__":
    main()
