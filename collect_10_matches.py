#!/usr/bin/env python3
"""
Собираем 10 последних матчей БЕЗ ФИЛЬТРОВ
Просто берем что дает скрапер
"""

import os
import sqlite3
import time
from datetime import datetime

def collect_recent_matches():
    """Собираем последние матчи без фильтров"""
    print("🚀 СБОР 10 ПОСЛЕДНИХ МАТЧЕЙ БЕЗ ФИЛЬТРОВ")
    print("=" * 50)
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        # Создаем скрапер
        scraper = MatchDataScraper(db_path='database/recent_10_matches.db')
        print("✅ MatchDataScraper создан")
        
        # Пробуем разные методы сбора
        print("\n🔄 Метод 1: update за последние 3 дня...")
        try:
            result1 = scraper.update(days=3, batch_size=10, headless=True)
            print(f"✅ update(days=3): {result1}")
        except Exception as e:
            print(f"❌ update: {e}")
        
        print("\n🔄 Метод 2: прямой scrape...")
        try:
            result2 = scraper.scrape(batch_size=10, headless=True)
            print(f"✅ scrape(): {result2}")
        except Exception as e:
            print(f"❌ scrape: {e}")
        
        print("\n🔄 Метод 3: предстоящие матчи...")
        try:
            result3 = scraper.update_upcoming_fixtures(days=7, batch_size=10, headless=True)
            print(f"✅ upcoming_fixtures: {result3}")
        except Exception as e:
            print(f"❌ upcoming_fixtures: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        return False

def check_collected_data():
    """Проверяем что собрали"""
    print("\n🔍 ПРОВЕРЯЕМ СОБРАННЫЕ ДАННЫЕ")
    print("=" * 35)
    
    db_path = 'database/recent_10_matches.db'
    
    if not os.path.exists(db_path):
        print("❌ База данных не создана")
        return 0
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем все таблицы
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 Создано таблиц: {len(tables)}")
        
        total_matches = 0
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"   • {table_name}: {count} записей")
            
            if table_name == 'match_data' and count > 0:
                total_matches = count
                
                # Показываем последние матчи
                print(f"\n📋 ПОСЛЕДНИЕ МАТЧИ:")
                cursor.execute("""
                    SELECT home_team, away_team, datetime, home_goals, away_goals, 
                           country, league, additional_data
                    FROM match_data 
                    ORDER BY datetime DESC 
                    LIMIT 10
                """)
                
                matches = cursor.fetchall()
                
                for i, match in enumerate(matches, 1):
                    home, away, dt, h_goals, a_goals, country, league, add_data = match
                    score = f"{h_goals}-{a_goals}" if h_goals is not None else "N/A"
                    has_data = "✅" if add_data else "❌"
                    
                    print(f"   {i}. {home} vs {away}")
                    print(f"      📅 {dt}")
                    print(f"      ⚽ {score} | 🏆 {league} ({country})")
                    print(f"      📝 Доп.данные: {has_data}")
                    
                    # Если есть дополнительные данные, показываем кусочек
                    if add_data:
                        data_preview = str(add_data)[:100] + "..." if len(str(add_data)) > 100 else str(add_data)
                        print(f"      📋 {data_preview}")
                    
                    print()
        
        conn.close()
        return total_matches
        
    except Exception as e:
        print(f"❌ Ошибка проверки данных: {e}")
        return 0

def try_alternative_scrapers():
    """Пробуем альтернативные скраперы"""
    print("\n🔄 ПРОБУЕМ АЛЬТЕРНАТИВНЫЕ СКРАПЕРЫ")
    print("=" * 40)
    
    # MatchIDScraper
    try:
        from flashscore_scraper import MatchIDScraper
        
        # Создаем минимальный конфиг
        os.makedirs('config', exist_ok=True)
        config_content = """
sport_ids:
  1:
    leagues: []
"""
        with open('config/minimal.yaml', 'w') as f:
            f.write(config_content)
        
        scraper = MatchIDScraper(
            config_path='config/minimal.yaml',
            db_path='database/recent_10_matches.db'
        )
        print("✅ MatchIDScraper создан")
        
        try:
            result = scraper.scrape()
            print(f"✅ MatchIDScraper.scrape(): {result}")
        except Exception as e:
            print(f"❌ MatchIDScraper.scrape(): {e}")
        
    except Exception as e:
        print(f"❌ MatchIDScraper: {e}")
    
    # OddsDataScraper
    try:
        from flashscore_scraper import OddsDataScraper
        
        scraper = OddsDataScraper(db_path='database/recent_10_matches.db')
        print("✅ OddsDataScraper создан")
        
        try:
            result = scraper.scrape()
            print(f"✅ OddsDataScraper.scrape(): {result}")
        except Exception as e:
            print(f"❌ OddsDataScraper.scrape(): {e}")
        
    except Exception as e:
        print(f"❌ OddsDataScraper: {e}")

def try_sport_classes():
    """Пробуем классы видов спорта"""
    print("\n⚽ ПРОБУЕМ КЛАССЫ ВИДОВ СПОРТА")
    print("=" * 35)
    
    sports = ['Football', 'Handball', 'Volleyball']
    
    for sport in sports:
        try:
            module = __import__('flashscore_scraper', fromlist=[sport])
            sport_class = getattr(module, sport)
            
            sport_instance = sport_class()
            print(f"✅ {sport} создан")
            
            # Пробуем методы
            methods_to_try = ['load_matches', 'load_fixtures', 'load_odds']
            
            for method_name in methods_to_try:
                if hasattr(sport_instance, method_name):
                    try:
                        method = getattr(sport_instance, method_name)
                        result = method()
                        print(f"✅ {sport}.{method_name}(): {result}")
                    except Exception as e:
                        print(f"❌ {sport}.{method_name}(): {e}")
            
        except Exception as e:
            print(f"❌ {sport}: {e}")

def main():
    """Главная функция сбора 10 матчей"""
    print("🎯 СБОР 10 ПОСЛЕДНИХ МАТЧЕЙ")
    print("Цель: Получить ЛЮБЫЕ данные без фильтров")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # 1. Основной сбор данных
    collection_success = collect_recent_matches()
    
    # 2. Проверяем что собрали
    matches_count = check_collected_data()
    
    # 3. Если основные методы не дали результата, пробуем альтернативные
    if matches_count == 0:
        print("\n🔄 Основные методы не дали результата, пробуем альтернативные...")
        try_alternative_scrapers()
        try_sport_classes()
        
        # Проверяем еще раз
        matches_count = check_collected_data()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 60)
    print("📊 ИТОГИ СБОРА:")
    print(f"   • Время выполнения: {duration:.1f} секунд")
    print(f"   • Собрано матчей: {matches_count}")
    print(f"   • База данных: database/recent_10_matches.db")
    
    if matches_count > 0:
        print(f"\n🎉 УСПЕХ! Собрано {matches_count} матчей!")
        print("📋 Следующие шаги:")
        print("   1. Проверить additional_data на наличие пенальти")
        print("   2. Использовать data loaders для анализа")
        print("   3. Расширить сбор данных")
    else:
        print(f"\n❌ Данные не собраны")
        print("💡 Возможные причины:")
        print("   • Нет подключения к интернету")
        print("   • FlashScore блокирует запросы")
        print("   • Нужна другая конфигурация")
        print("   • Требуется браузер Chrome/Firefox")

if __name__ == "__main__":
    main()
