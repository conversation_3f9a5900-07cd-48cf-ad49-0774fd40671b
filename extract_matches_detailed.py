#!/usr/bin/env python3
"""
Детальное извлечение матчей с FlashScore
Ищем конкретные данные матчей
"""

import requests
from bs4 import BeautifulSoup
import re
import json

def find_match_data():
    """Ищем конкретные данные матчей"""
    
    url = "https://www.flashscore.com/football/kazakhstan/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"🔄 Получаем данные с FlashScore Kazakhstan...")
        response = requests.get(url, headers=headers, timeout=10)
        print(f"✅ Статус: {response.status_code}, Размер: {len(response.text)} символов")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Ищем переменные JavaScript с данными матчей
            print(f"\n🔍 ПОИСК ПЕРЕМЕННЫХ С ДАННЫМИ МАТЧЕЙ:")
            
            scripts = soup.find_all('script')
            match_data_found = []
            
            for script in scripts:
                if script.string:
                    script_content = script.string
                    
                    # Ищем переменные которые могут содержать данные матчей
                    patterns = [
                        r'matches\s*=\s*([^;]+);',
                        r'fixtures\s*=\s*([^;]+);',
                        r'events\s*=\s*([^;]+);',
                        r'games\s*=\s*([^;]+);',
                        r'matchData\s*=\s*([^;]+);',
                        r'tournament_data\s*=\s*([^;]+);',
                        r'league_data\s*=\s*([^;]+);'
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, script_content, re.IGNORECASE)
                        if matches:
                            print(f"   ✅ Найдена переменная: {pattern.split('\\')[0]}")
                            for match in matches:
                                if len(match) > 50:  # Только большие данные
                                    match_data_found.append({
                                        'type': pattern.split('\\')[0],
                                        'data': match[:500] + "..." if len(match) > 500 else match
                                    })
            
            # Ищем данные в window объекте
            print(f"\n🔍 ПОИСК WINDOW ОБЪЕКТОВ:")
            window_patterns = [
                r'window\.([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(\{[^}]+\}|\[[^\]]+\])',
                r'window\[[\'""]([^"\']+)[\'"\"]\]\s*=\s*(\{[^}]+\}|\[[^\]]+\])'
            ]
            
            for script in scripts:
                if script.string:
                    for pattern in window_patterns:
                        matches = re.findall(pattern, script.string)
                        for var_name, data in matches:
                            if any(keyword in var_name.lower() for keyword in ['match', 'event', 'fixture', 'game']):
                                print(f"   ✅ window.{var_name}: {data[:100]}...")
                                match_data_found.append({
                                    'type': f'window.{var_name}',
                                    'data': data
                                })
            
            # Ищем Ajax/API endpoints
            print(f"\n🔍 ПОИСК API ENDPOINTS:")
            api_patterns = [
                r'["\']([^"\']*api[^"\']*)["\']',
                r'["\']([^"\']*ajax[^"\']*)["\']',
                r'["\']([^"\']*feed[^"\']*)["\']',
                r'url\s*:\s*["\']([^"\']+)["\']'
            ]
            
            api_endpoints = set()
            for script in scripts:
                if script.string:
                    for pattern in api_patterns:
                        endpoints = re.findall(pattern, script.string, re.IGNORECASE)
                        for endpoint in endpoints:
                            if any(keyword in endpoint.lower() for keyword in ['match', 'event', 'fixture', 'game', 'soccer', 'football']):
                                api_endpoints.add(endpoint)
            
            if api_endpoints:
                print(f"   ✅ Найдено API endpoints:")
                for endpoint in list(api_endpoints)[:5]:
                    print(f"      • {endpoint}")
            
            # Ищем ID матчей в HTML
            print(f"\n🔍 ПОИСК ID МАТЧЕЙ:")
            id_patterns = [
                r'match[_-]?id["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
                r'event[_-]?id["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
                r'fixture[_-]?id["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
                r'g_([0-9]+)_',  # FlashScore часто использует g_ID_
                r'id="g_([0-9]+)"'
            ]
            
            match_ids = set()
            full_text = response.text
            
            for pattern in id_patterns:
                ids = re.findall(pattern, full_text, re.IGNORECASE)
                match_ids.update(ids)
            
            if match_ids:
                print(f"   ✅ Найдено ID матчей: {len(match_ids)}")
                for match_id in list(match_ids)[:10]:
                    print(f"      • {match_id}")
            
            # Ищем названия команд
            print(f"\n🔍 ПОИСК КОМАНД КАЗАХСТАНА:")
            kz_teams = [
                'Astana', 'Kairat', 'Shakhter', 'Tobol', 'Ordabasy', 'Aktobe',
                'Zhetysu', 'Kaisar', 'Atyrau', 'Taraz', 'Caspiy', 'Akzhayik'
            ]
            
            found_teams = []
            for team in kz_teams:
                if team.lower() in full_text.lower():
                    found_teams.append(team)
                    print(f"   ✅ {team}")
                    
                    # Ищем контекст команды
                    pattern = rf'.{{0,100}}{re.escape(team)}.{{0,100}}'
                    contexts = re.findall(pattern, full_text, re.IGNORECASE)
                    if contexts:
                        for context in contexts[:2]:
                            clean_context = re.sub(r'<[^>]+>', '', context)
                            if len(clean_context.strip()) > 10:
                                print(f"      Контекст: {clean_context.strip()[:150]}...")
            
            # Показываем найденные данные матчей
            print(f"\n📊 НАЙДЕННЫЕ ДАННЫЕ МАТЧЕЙ:")
            if match_data_found:
                for i, data in enumerate(match_data_found[:5], 1):
                    print(f"   {i}. Тип: {data['type']}")
                    print(f"      Данные: {data['data'][:200]}...")
                    print()
            else:
                print("   ❌ Данные матчей не найдены в переменных JavaScript")
            
            return {
                'match_data_vars': len(match_data_found),
                'api_endpoints': len(api_endpoints),
                'match_ids': len(match_ids),
                'found_teams': len(found_teams),
                'teams': found_teams
            }
            
        else:
            print(f"❌ HTTP ошибка: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return None

def try_direct_api_calls():
    """Пробуем прямые вызовы к API FlashScore"""
    print(f"\n🌐 ПРОБУЕМ ПРЯМЫЕ API ВЫЗОВЫ:")
    
    # Возможные API endpoints FlashScore
    api_urls = [
        "https://www.flashscore.com/x/feed/",
        "https://www.flashscore.com/x/feed/f_1_102_",  # 102 = Kazakhstan
        "https://d.flashscore.com/x/feed/",
        "https://www.flashscore.com/ajax/",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.flashscore.com/football/kazakhstan/',
        'X-Requested-With': 'XMLHttpRequest'
    }
    
    for url in api_urls:
        try:
            print(f"   🔄 Пробуем: {url}")
            response = requests.get(url, headers=headers, timeout=5)
            print(f"      Статус: {response.status_code}")
            
            if response.status_code == 200 and len(response.text) > 100:
                print(f"      Размер: {len(response.text)} символов")
                print(f"      Превью: {response.text[:150]}...")
                
                # Ищем данные матчей в ответе
                if any(keyword in response.text.lower() for keyword in ['match', 'event', 'fixture', 'team']):
                    print(f"      ✅ Содержит данные матчей!")
                
        except Exception as e:
            print(f"      ❌ Ошибка: {e}")

def main():
    """Главная функция"""
    print("🎯 ДЕТАЛЬНОЕ ИЗВЛЕЧЕНИЕ МАТЧЕЙ С FLASHSCORE")
    print("=" * 60)
    
    # Основной поиск
    result = find_match_data()
    
    # Пробуем API вызовы
    try_direct_api_calls()
    
    if result:
        print(f"\n🎯 ИТОГОВАЯ СТАТИСТИКА:")
        for key, value in result.items():
            print(f"   • {key}: {value}")
        
        if result['found_teams'] > 0:
            print(f"\n🎉 УСПЕХ! Найдены казахстанские команды!")
            print("Это означает, что на странице есть данные о матчах")
        else:
            print(f"\n⚠️  Команды не найдены - возможно нет текущих матчей")
    else:
        print(f"\n❌ Не удалось получить данные")

if __name__ == "__main__":
    main()
