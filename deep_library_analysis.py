#!/usr/bin/env python3
"""
ГЛУБОКИЙ АНАЛИЗ БИБЛИОТЕКИ flashscore-scraper
Смотрим ВСЕ что она может дать
"""

import inspect
import os
import sqlite3

def analyze_all_modules():
    """Анализируем все модули библиотеки"""
    print("🔍 ГЛУБОКИЙ АНАЛИЗ ВСЕХ МОДУЛЕЙ")
    print("=" * 40)
    
    try:
        import flashscore_scraper
        
        # Основные атрибуты
        print("📦 ОСНОВНЫЕ АТРИБУТЫ:")
        for attr in dir(flashscore_scraper):
            if not attr.startswith('_'):
                obj = getattr(flashscore_scraper, attr)
                print(f"   • {attr}: {type(obj).__name__}")
        
        # Изучаем модули
        modules_to_check = ['core', 'data_loaders', 'exceptions', 'models', 'scrapers']
        
        for module_name in modules_to_check:
            if hasattr(flashscore_scraper, module_name):
                print(f"\n📁 МОДУЛЬ: {module_name}")
                module = getattr(flashscore_scraper, module_name)
                
                for attr in dir(module):
                    if not attr.startswith('_'):
                        obj = getattr(module, attr)
                        print(f"   • {attr}: {type(obj).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка анализа модулей: {e}")
        return False

def test_browser_manager():
    """Тестируем BrowserManager"""
    print("\n🌐 ТЕСТИРУЕМ BROWSER MANAGER")
    print("-" * 30)
    
    try:
        from flashscore_scraper import BrowserManager
        
        print("✅ BrowserManager импортирован")
        
        # Смотрим методы
        methods = [m for m in dir(BrowserManager) if not m.startswith('_')]
        print(f"📋 Методы ({len(methods)}):")
        for method in methods:
            print(f"   • {method}")
        
        # Пробуем создать экземпляр
        try:
            browser = BrowserManager()
            print("✅ BrowserManager создан")
            
            # Пробуем получить драйвер
            try:
                driver = browser.get_driver()
                print("✅ Драйвер получен")
                print(f"   Тип драйвера: {type(driver)}")
                
                # Закрываем
                browser.close()
                print("✅ Браузер закрыт")
                
            except Exception as e:
                print(f"❌ Ошибка получения драйвера: {e}")
                
        except Exception as e:
            print(f"❌ Ошибка создания BrowserManager: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка BrowserManager: {e}")
        return False

def test_database_manager():
    """Тестируем DatabaseManager"""
    print("\n🗄️ ТЕСТИРУЕМ DATABASE MANAGER")
    print("-" * 30)
    
    try:
        from flashscore_scraper import DatabaseManager
        
        print("✅ DatabaseManager импортирован")
        
        # Смотрим методы
        methods = [m for m in dir(DatabaseManager) if not m.startswith('_')]
        print(f"📋 Методы ({len(methods)}):")
        for method in methods:
            print(f"   • {method}")
        
        # Пробуем создать экземпляр
        try:
            db = DatabaseManager(db_path='database/test_db_manager.db')
            print("✅ DatabaseManager создан")
            
            # Пробуем методы
            try:
                cursor = db.get_cursor()
                print("✅ Курсор получен")
                
                # Проверяем соединение
                if db.is_connection_valid():
                    print("✅ Соединение валидно")
                else:
                    print("❌ Соединение не валидно")
                
                db.close()
                print("✅ База данных закрыта")
                
            except Exception as e:
                print(f"❌ Ошибка работы с БД: {e}")
                
        except Exception as e:
            print(f"❌ Ошибка создания DatabaseManager: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка DatabaseManager: {e}")
        return False

def analyze_scrapers_in_detail():
    """Детальный анализ скраперов"""
    print("\n🕷️ ДЕТАЛЬНЫЙ АНАЛИЗ СКРАПЕРОВ")
    print("-" * 35)
    
    scrapers = [
        ('MatchDataScraper', 'flashscore_scraper', 'MatchDataScraper'),
        ('MatchIDScraper', 'flashscore_scraper', 'MatchIDScraper'),
        ('OddsDataScraper', 'flashscore_scraper', 'OddsDataScraper')
    ]
    
    for scraper_name, module_name, class_name in scrapers:
        print(f"\n🎯 {scraper_name}:")
        
        try:
            module = __import__(module_name, fromlist=[class_name])
            scraper_class = getattr(module, class_name)
            
            # Анализируем конструктор
            try:
                sig = inspect.signature(scraper_class.__init__)
                print(f"   📝 Конструктор: {scraper_name}{sig}")
            except:
                print(f"   📝 Конструктор: {scraper_name}(...)")
            
            # Анализируем методы
            methods = [m for m in dir(scraper_class) if not m.startswith('_') and callable(getattr(scraper_class, m))]
            print(f"   📋 Методы ({len(methods)}):")
            
            for method in methods:
                try:
                    method_obj = getattr(scraper_class, method)
                    sig = inspect.signature(method_obj)
                    print(f"      • {method}{sig}")
                    
                    # Показываем документацию если есть
                    if method_obj.__doc__:
                        doc = method_obj.__doc__.strip().split('\n')[0]
                        print(f"        └─ {doc}")
                        
                except:
                    print(f"      • {method}(...)")
            
            # Пробуем создать экземпляр
            try:
                if scraper_name == 'MatchIDScraper':
                    # Создаем минимальный конфиг
                    os.makedirs('config', exist_ok=True)
                    with open('config/minimal.yaml', 'w') as f:
                        f.write('sport_ids:\n  1:\n    leagues: []')
                    
                    scraper = scraper_class(config_path='config/minimal.yaml', db_path='database/test.db')
                else:
                    scraper = scraper_class(db_path='database/test.db')
                
                print(f"   ✅ Экземпляр {scraper_name} создан")
                
                # Пробуем вызвать методы
                safe_methods = ['get_database', 'get_browser']
                for safe_method in safe_methods:
                    if hasattr(scraper, safe_method):
                        try:
                            if safe_method == 'get_browser':
                                result = getattr(scraper, safe_method)(headless=True)
                                print(f"   ✅ {safe_method}() работает: {type(result)}")
                                if hasattr(result, 'close'):
                                    result.close()
                            else:
                                result = getattr(scraper, safe_method)()
                                print(f"   ✅ {safe_method}() работает: {type(result)}")
                                if hasattr(result, 'close'):
                                    result.close()
                        except Exception as e:
                            print(f"   ❌ {safe_method}(): {e}")
                
            except Exception as e:
                print(f"   ❌ Не удалось создать экземпляр: {e}")
            
        except Exception as e:
            print(f"   ❌ Ошибка анализа {scraper_name}: {e}")

def check_real_functionality():
    """Проверяем реальную функциональность"""
    print("\n🔬 ПРОВЕРКА РЕАЛЬНОЙ ФУНКЦИОНАЛЬНОСТИ")
    print("-" * 40)
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        scraper = MatchDataScraper(db_path='database/functionality_test.db')
        print("✅ Скрапер создан")
        
        # Проверяем что создается в базе данных
        db = scraper.get_database()
        cursor = db.get_cursor()
        
        # Смотрим какие таблицы создаются
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 Создано таблиц: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n🗂️ Таблица: {table_name}")
            
            # Получаем структуру таблицы
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("   Колонки:")
            for col in columns:
                col_name, col_type, not_null, default, pk = col[1], col[2], col[3], col[4], col[5]
                nullable = "NOT NULL" if not_null else "NULL"
                primary = "PK" if pk else ""
                default_val = f"DEFAULT {default}" if default else ""
                print(f"      • {col_name} ({col_type}) {nullable} {primary} {default_val}")
        
        db.close()
        
        # Пробуем execute_query
        print(f"\n🔍 Тестируем execute_query:")
        try:
            result = scraper.execute_query("SELECT COUNT(*) FROM match_data;")
            print(f"   ✅ execute_query работает: {result}")
        except Exception as e:
            print(f"   ❌ execute_query: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка проверки функциональности: {e}")
        return False

def main():
    """Главная функция глубокого анализа"""
    print("🔬 ГЛУБОКИЙ АНАЛИЗ БИБЛИОТЕКИ FLASHSCORE-SCRAPER")
    print("=" * 60)
    print("Цель: Понять ВСЕ возможности библиотеки")
    print("=" * 60)
    
    # 1. Анализ всех модулей
    analyze_all_modules()
    
    # 2. Тест BrowserManager
    test_browser_manager()
    
    # 3. Тест DatabaseManager
    test_database_manager()
    
    # 4. Детальный анализ скраперов
    analyze_scrapers_in_detail()
    
    # 5. Проверка реальной функциональности
    check_real_functionality()
    
    print("\n" + "=" * 60)
    print("🎯 ВЫВОДЫ О БИБЛИОТЕКЕ:")
    print("✅ Библиотека имеет полную структуру для скрапинга")
    print("✅ Поддерживает браузерную автоматизацию (Selenium)")
    print("✅ Имеет систему управления базой данных")
    print("✅ Создает правильную структуру таблиц")
    print("❓ НО: Не собирает реальные данные (возможно нужна настройка)")
    
    print("\n📋 ЧТО БИБЛИОТЕКА ТОЧНО УМЕЕТ:")
    print("   • Создавать и управлять базой данных SQLite")
    print("   • Запускать браузер через Selenium")
    print("   • Создавать таблицы для матчей, коэффициентов, событий")
    print("   • Выполнять SQL запросы")
    print("   • Управлять сессиями скрапинга")
    
    print("\n❓ ЧТО НЕЯСНО:")
    print("   • Почему не собирает реальные данные")
    print("   • Нужна ли специальная конфигурация")
    print("   • Требуется ли авторизация на FlashScore")
    print("   • Работает ли вообще с текущей версией FlashScore")

if __name__ == "__main__":
    main()
