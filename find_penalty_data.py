#!/usr/bin/env python3
"""
Поиск данных о пенальти в существующих базах данных
Анализируем поле additional_data на предмет информации о пенальти
"""

import os
import sqlite3
import json
from pathlib import Path

def analyze_database_for_penalties(db_path):
    """Анализ конкретной базы данных на предмет пенальти"""
    print(f"\n🔍 Анализ базы данных: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ База данных не найдена")
        return []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Проверяем наличие таблицы match_data
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='match_data';")
        if not cursor.fetchone():
            print("❌ Таблица match_data не найдена")
            conn.close()
            return []
        
        # Получаем все матчи с дополнительными данными
        cursor.execute("""
            SELECT 
                flashscore_id,
                country,
                league,
                datetime,
                home_team,
                away_team,
                home_goals,
                away_goals,
                result,
                additional_data
            FROM match_data 
            WHERE additional_data IS NOT NULL AND additional_data != ''
            ORDER BY datetime DESC
        """)
        
        matches = cursor.fetchall()
        print(f"📊 Найдено матчей с дополнительными данными: {len(matches)}")
        
        penalty_matches = []
        
        for match in matches:
            (flashscore_id, country, league, match_datetime, home_team, away_team, 
             home_goals, away_goals, result, additional_data) = match
            
            # Анализируем additional_data на предмет пенальти
            penalty_info = analyze_additional_data_for_penalties(additional_data)
            
            if penalty_info['has_penalty']:
                penalty_match = {
                    'id': flashscore_id,
                    'country': country,
                    'league': league,
                    'datetime': match_datetime,
                    'home_team': home_team,
                    'away_team': away_team,
                    'score': f"{home_goals}-{away_goals}" if home_goals is not None else "N/A",
                    'result': result,
                    'penalty_info': penalty_info,
                    'raw_data': additional_data
                }
                penalty_matches.append(penalty_match)
        
        conn.close()
        return penalty_matches
        
    except Exception as e:
        print(f"❌ Ошибка при анализе базы данных: {e}")
        return []

def analyze_additional_data_for_penalties(additional_data):
    """Анализ JSON данных на предмет пенальти"""
    penalty_info = {
        'has_penalty': False,
        'penalty_type': '',
        'penalty_details': [],
        'penalty_shootout': False,
        'penalty_goals': [],
        'events': []
    }
    
    if not additional_data:
        return penalty_info
    
    try:
        # Пробуем парсить JSON
        if isinstance(additional_data, str):
            data = json.loads(additional_data)
        else:
            data = additional_data
        
        # Конвертируем в строку для поиска ключевых слов
        data_str = str(data).lower()
        
        # Ключевые слова для поиска пенальти
        penalty_keywords = [
            'penalty', 'penalties', 'пенальти', 'pen', 'pk',
            'penalty_shootout', 'shootout', 'penalty kick',
            'penalty goal', 'penalty miss', 'penalty saved'
        ]
        
        # Проверяем наличие ключевых слов
        found_keywords = []
        for keyword in penalty_keywords:
            if keyword in data_str:
                found_keywords.append(keyword)
                penalty_info['has_penalty'] = True
        
        if found_keywords:
            penalty_info['penalty_type'] = ', '.join(found_keywords)
        
        # Ищем конкретные структуры данных
        if isinstance(data, dict):
            # Ищем события матча
            if 'events' in data:
                events = data['events']
                if isinstance(events, list):
                    for event in events:
                        if isinstance(event, dict):
                            event_str = str(event).lower()
                            if any(keyword in event_str for keyword in penalty_keywords):
                                penalty_info['events'].append(event)
                                penalty_info['has_penalty'] = True
            
            # Ищем голы
            if 'goals' in data:
                goals = data['goals']
                if isinstance(goals, list):
                    for goal in goals:
                        if isinstance(goal, dict):
                            goal_str = str(goal).lower()
                            if any(keyword in goal_str for keyword in penalty_keywords):
                                penalty_info['penalty_goals'].append(goal)
                                penalty_info['has_penalty'] = True
            
            # Ищем серию пенальти
            penalty_shootout_keys = ['penalty_shootout', 'shootout', 'penalties']
            for key in penalty_shootout_keys:
                if key in data:
                    penalty_info['penalty_shootout'] = True
                    penalty_info['penalty_details'].append(f"{key}: {data[key]}")
                    penalty_info['has_penalty'] = True
        
        # Дополнительный анализ по тексту
        if penalty_info['has_penalty']:
            penalty_info['penalty_details'].append(f"Найдены ключевые слова: {found_keywords}")
        
    except json.JSONDecodeError:
        # Если не JSON, ищем в тексте
        data_str = str(additional_data).lower()
        penalty_keywords = ['penalty', 'пенальти', 'pen', 'pk']
        
        found_keywords = [kw for kw in penalty_keywords if kw in data_str]
        if found_keywords:
            penalty_info['has_penalty'] = True
            penalty_info['penalty_type'] = 'text_search'
            penalty_info['penalty_details'] = [f"Найдено в тексте: {found_keywords}"]
    
    except Exception as e:
        penalty_info['penalty_details'].append(f"Ошибка анализа: {e}")
    
    return penalty_info

def search_all_databases():
    """Поиск пенальти во всех базах данных"""
    print("🔍 ПОИСК ДАННЫХ О ПЕНАЛЬТИ ВО ВСЕХ БАЗАХ ДАННЫХ")
    print("=" * 60)
    
    database_dir = Path('database')
    if not database_dir.exists():
        print("❌ Директория database не найдена")
        return
    
    # Получаем все .db файлы
    db_files = list(database_dir.glob('*.db'))
    print(f"📁 Найдено баз данных: {len(db_files)}")
    
    all_penalty_matches = []
    
    for db_file in db_files:
        penalty_matches = analyze_database_for_penalties(str(db_file))
        if penalty_matches:
            print(f"✅ В {db_file.name} найдено матчей с пенальти: {len(penalty_matches)}")
            all_penalty_matches.extend(penalty_matches)
        else:
            print(f"❌ В {db_file.name} пенальти не найдены")
    
    return all_penalty_matches

def display_penalty_matches(matches):
    """Отображение найденных матчей с пенальти"""
    if not matches:
        print("\n❌ МАТЧИ С ПЕНАЛЬТИ НЕ НАЙДЕНЫ")
        return
    
    print(f"\n🎯 НАЙДЕНО МАТЧЕЙ С ПЕНАЛЬТИ: {len(matches)}")
    print("=" * 80)
    
    for i, match in enumerate(matches[:10], 1):  # Показываем первые 10
        print(f"\n{i}. ⚽ {match['home_team']} vs {match['away_team']}")
        print(f"   📅 Дата: {match['datetime']}")
        print(f"   🏟️  Лига: {match['league']} ({match['country']})")
        print(f"   ⚽ Счет: {match['score']}")
        print(f"   🆔 ID: {match['id']}")
        
        penalty_info = match['penalty_info']
        print(f"   🥅 Тип пенальти: {penalty_info['penalty_type']}")
        
        if penalty_info['penalty_shootout']:
            print(f"   🎯 Серия пенальти: ДА")
        
        if penalty_info['penalty_goals']:
            print(f"   ⚽ Голы с пенальти: {len(penalty_info['penalty_goals'])}")
        
        if penalty_info['events']:
            print(f"   📋 События с пенальти: {len(penalty_info['events'])}")
        
        for detail in penalty_info['penalty_details'][:2]:  # Показываем первые 2 детали
            print(f"   📝 {detail}")
        
        print("-" * 60)

def create_penalty_report(matches):
    """Создание отчета о найденных пенальти"""
    if not matches:
        return
    
    report_content = f"""# ОТЧЕТ О НАЙДЕННЫХ ПЕНАЛЬТИ
Дата создания: {os.popen('date').read().strip()}

## СТАТИСТИКА
- Всего найдено матчей с пенальти: {len(matches)}
- Источник: Базы данных flashscore-scraper

## НАЙДЕННЫЕ МАТЧИ

"""
    
    for i, match in enumerate(matches, 1):
        report_content += f"""
### {i}. {match['home_team']} vs {match['away_team']}
- **Дата:** {match['datetime']}
- **Лига:** {match['league']} ({match['country']})
- **Счет:** {match['score']}
- **ID:** {match['id']}
- **Тип пенальти:** {match['penalty_info']['penalty_type']}
- **Серия пенальти:** {'Да' if match['penalty_info']['penalty_shootout'] else 'Нет'}

**Детали:**
"""
        for detail in match['penalty_info']['penalty_details']:
            report_content += f"- {detail}\n"
        
        report_content += "\n---\n"
    
    with open('penalty_matches_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ Отчет сохранен в penalty_matches_report.md")

def main():
    """Главная функция поиска пенальти"""
    print("🥅 ПОИСК ДАННЫХ О ПЕНАЛЬТИ В FLASHSCORE-SCRAPER")
    print("=" * 60)
    
    # Ищем во всех базах данных
    penalty_matches = search_all_databases()
    
    # Отображаем результаты
    display_penalty_matches(penalty_matches)
    
    # Создаем отчет
    if penalty_matches:
        create_penalty_report(penalty_matches)
        
        print(f"\n📊 ИТОГОВАЯ СТАТИСТИКА:")
        print(f"   • Всего найдено матчей с пенальти: {len(penalty_matches)}")
        
        # Группируем по типам пенальти
        penalty_types = {}
        for match in penalty_matches:
            p_type = match['penalty_info']['penalty_type']
            penalty_types[p_type] = penalty_types.get(p_type, 0) + 1
        
        print(f"   • Типы найденных пенальти:")
        for p_type, count in penalty_types.items():
            print(f"     - {p_type}: {count} матчей")
        
        print(f"\n📁 СОЗДАННЫЕ ФАЙЛЫ:")
        print(f"   • penalty_matches_report.md - детальный отчет")
    else:
        print(f"\n💡 РЕКОМЕНДАЦИИ:")
        print(f"   • Сначала соберите данные матчей с помощью MatchDataScraper")
        print(f"   • Убедитесь, что additional_data содержит детальную информацию")
        print(f"   • Попробуйте собрать данные за больший период времени")

if __name__ == "__main__":
    main()
