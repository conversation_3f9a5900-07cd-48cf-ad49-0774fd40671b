#!/usr/bin/env python3
"""
Изучение библиотеки flashscore-scraper на предмет данных о пенальти
Ищем в модулях, классах и методах информацию о пенальти
"""

import inspect
import json
from pprint import pprint

def explore_library_for_penalties():
    """Изучаем библиотеку на предмет данных о пенальти"""
    print("🔍 ПОИСК ДАННЫХ О ПЕНАЛЬТИ В БИБЛИОТЕКЕ")
    print("=" * 50)
    
    try:
        import flashscore_scraper
        
        # Получаем все атрибуты библиотеки
        all_attrs = dir(flashscore_scraper)
        print(f"📦 Всего атрибутов в библиотеке: {len(all_attrs)}")
        
        # Ищем что-то связанное с пенальти
        penalty_related = []
        for attr in all_attrs:
            if any(keyword in attr.lower() for keyword in ['penalty', 'pen', 'pk', 'shoot']):
                penalty_related.append(attr)
        
        if penalty_related:
            print(f"🎯 Найдены атрибуты связанные с пенальти: {penalty_related}")
        else:
            print("❌ Прямых упоминаний пенальти в основных атрибутах не найдено")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при изучении библиотеки: {e}")
        return False

def explore_models_for_penalties():
    """Изучаем модели данных на предмет пенальти"""
    print("\n🏗️ ИЗУЧЕНИЕ МОДЕЛЕЙ ДАННЫХ")
    print("-" * 30)
    
    try:
        from flashscore_scraper import models
        
        # Изучаем все модели
        model_attrs = [attr for attr in dir(models) if not attr.startswith('_')]
        print(f"📋 Найдено моделей: {len(model_attrs)}")
        
        for model_name in model_attrs:
            model_obj = getattr(models, model_name)
            
            if inspect.isclass(model_obj):
                print(f"\n📊 Модель: {model_name}")
                
                # Изучаем поля модели
                if hasattr(model_obj, '__annotations__'):
                    annotations = model_obj.__annotations__
                    print("   Поля:")
                    for field, field_type in annotations.items():
                        print(f"   • {field}: {field_type}")
                        
                        # Ищем поля связанные с пенальти
                        if any(keyword in field.lower() for keyword in ['penalty', 'pen', 'pk', 'shoot', 'additional']):
                            print(f"     🎯 ВОЗМОЖНО СОДЕРЖИТ ДАННЫЕ О ПЕНАЛЬТИ!")
                
                # Изучаем методы модели
                methods = [method for method in dir(model_obj) if not method.startswith('_')]
                if methods:
                    print("   Методы:")
                    for method in methods[:5]:  # Показываем первые 5
                        print(f"   • {method}()")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при изучении моделей: {e}")
        return False

def explore_scrapers_for_penalties():
    """Изучаем скраперы на предмет методов сбора пенальти"""
    print("\n🕷️ ИЗУЧЕНИЕ СКРАПЕРОВ")
    print("-" * 25)
    
    try:
        from flashscore_scraper import MatchDataScraper, MatchIDScraper, OddsDataScraper
        
        scrapers = [
            ('MatchDataScraper', MatchDataScraper),
            ('MatchIDScraper', MatchIDScraper),
            ('OddsDataScraper', OddsDataScraper)
        ]
        
        for scraper_name, scraper_class in scrapers:
            print(f"\n🎯 {scraper_name}:")
            
            # Создаем экземпляр для изучения
            try:
                if scraper_name == 'MatchIDScraper':
                    scraper = scraper_class(config_path='config/test.yaml', db_path='test.db')
                else:
                    scraper = scraper_class(db_path='test.db')
                
                # Изучаем методы
                methods = [method for method in dir(scraper) if not method.startswith('_') and callable(getattr(scraper, method))]
                
                print("   Методы:")
                for method in methods:
                    print(f"   • {method}()")
                    
                    # Ищем методы связанные с пенальти
                    if any(keyword in method.lower() for keyword in ['penalty', 'pen', 'pk', 'shoot', 'additional', 'detail']):
                        print(f"     🎯 ВОЗМОЖНО СОБИРАЕТ ДАННЫЕ О ПЕНАЛЬТИ!")
                        
                        # Пробуем получить документацию метода
                        try:
                            method_obj = getattr(scraper, method)
                            if method_obj.__doc__:
                                print(f"     📝 Документация: {method_obj.__doc__[:100]}...")
                        except:
                            pass
                
            except Exception as e:
                print(f"   ❌ Не удалось создать экземпляр: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при изучении скраперов: {e}")
        return False

def explore_database_schema():
    """Изучаем схему базы данных на предмет полей с пенальти"""
    print("\n🗄️ ИЗУЧЕНИЕ СХЕМЫ БАЗЫ ДАННЫХ")
    print("-" * 35)
    
    try:
        from flashscore_scraper import MatchDataScraper
        
        # Создаем скрапер для доступа к базе данных
        scraper = MatchDataScraper(db_path='database/penalty_test.db')
        db = scraper.get_database()
        
        print("✅ Подключение к базе данных получено")
        
        # Получаем курсор
        cursor = db.get_cursor()
        
        # Создаем таблицы (если их нет) чтобы увидеть схему
        print("📊 Анализируем схему таблиц...")
        
        # Список возможных таблиц из предыдущих тестов
        possible_tables = [
            'match_data', 'flashscore_ids', 'odds_data', 
            'upcoming_fixtures', 'sport_ids', 'bookmaker_ids'
        ]
        
        for table_name in possible_tables:
            try:
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                if columns:
                    print(f"\n🗂️ Таблица: {table_name}")
                    print("   Колонки:")
                    
                    for col in columns:
                        col_name, col_type = col[1], col[2]
                        print(f"   • {col_name} ({col_type})")
                        
                        # Ищем поля которые могут содержать данные о пенальти
                        if any(keyword in col_name.lower() for keyword in 
                               ['penalty', 'pen', 'pk', 'shoot', 'additional', 'extra', 'detail', 'json']):
                            print(f"     🎯 МОЖЕТ СОДЕРЖАТЬ ДАННЫЕ О ПЕНАЛЬТИ!")
                            
                            if col_name.lower() == 'additional_data':
                                print(f"     📝 Это поле скорее всего содержит JSON с деталями матча!")
            except:
                pass  # Таблица не существует
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при изучении схемы БД: {e}")
        return False

def explore_additional_data_structure():
    """Изучаем структуру additional_data поля"""
    print("\n📋 ИЗУЧЕНИЕ ПОЛЯ ADDITIONAL_DATA")
    print("-" * 40)
    
    print("🔍 Поле 'additional_data' в таблице 'match_data' содержит JSON")
    print("Это поле может содержать:")
    print("   • Статистику матча")
    print("   • События матча (голы, карточки, замены)")
    print("   • Информацию о пенальти")
    print("   • Дополнительное время")
    print("   • Серию пенальти")
    print("   • Детали голов (включая пенальти)")
    
    print("\n🎯 ВОЗМОЖНЫЕ КЛЮЧИ В JSON ДЛЯ ПЕНАЛЬТИ:")
    possible_penalty_keys = [
        'penalties', 'penalty_shootout', 'pk', 'pen',
        'events', 'goals', 'match_events', 'statistics',
        'extra_time', 'shootout', 'penalty_goals'
    ]
    
    for key in possible_penalty_keys:
        print(f"   • '{key}' - может содержать данные о пенальти")
    
    return True

def create_penalty_search_strategy():
    """Создаем стратегию поиска пенальти"""
    print("\n📋 СТРАТЕГИЯ ПОИСКА ПЕНАЛЬТИ")
    print("=" * 40)
    
    strategy = """
🎯 ПЛАН ПОИСКА ДАННЫХ О ПЕНАЛЬТИ:

1. ОСНОВНОЕ ПОЛЕ ДЛЯ ПОИСКА:
   • additional_data (JSON) в таблице match_data
   
2. КЛЮЧЕВЫЕ СЛОВА ДЛЯ ПОИСКА В JSON:
   • "penalty", "penalties"
   • "pk", "pen" 
   • "shootout", "penalty_shootout"
   • "extra_time"
   • События с типом "penalty"
   
3. ДОПОЛНИТЕЛЬНЫЕ ПРИЗНАКИ:
   • Счет матча (ничья + результат не 0)
   • Дополнительное время
   • Необычные голы в конце матча
   
4. АЛГОРИТМ ПОИСКА:
   a) Собрать матчи с additional_data != NULL
   b) Парсить JSON в additional_data
   c) Искать ключевые слова
   d) Анализировать события матча
   e) Фильтровать по казахстанским командам
"""
    
    print(strategy)
    
    # Сохраняем стратегию в файл
    with open('penalty_search_strategy.md', 'w', encoding='utf-8') as f:
        f.write(strategy)
    
    print("✅ Стратегия сохранена в penalty_search_strategy.md")

def main():
    """Главная функция исследования"""
    print("🔍 ИССЛЕДОВАНИЕ БИБЛИОТЕКИ FLASHSCORE-SCRAPER")
    print("ЦЕЛЬ: Найти данные о пенальти")
    print("=" * 60)
    
    # 1. Изучаем основную библиотеку
    explore_library_for_penalties()
    
    # 2. Изучаем модели данных
    explore_models_for_penalties()
    
    # 3. Изучаем скраперы
    explore_scrapers_for_penalties()
    
    # 4. Изучаем схему базы данных
    explore_database_schema()
    
    # 5. Изучаем структуру additional_data
    explore_additional_data_structure()
    
    # 6. Создаем стратегию поиска
    create_penalty_search_strategy()
    
    print("\n" + "=" * 60)
    print("🎉 ИССЛЕДОВАНИЕ ЗАВЕРШЕНО!")
    print("\n📊 ВЫВОДЫ:")
    print("✅ Данные о пенальти СКОРЕЕ ВСЕГО есть в поле 'additional_data'")
    print("✅ Это JSON поле содержит детальную информацию о матче")
    print("✅ Нужно парсить JSON и искать ключевые слова")
    print("✅ Стратегия поиска создана")
    
    print("\n📁 СОЗДАННЫЕ ФАЙЛЫ:")
    print("   • penalty_search_strategy.md - стратегия поиска")

if __name__ == "__main__":
    main()
