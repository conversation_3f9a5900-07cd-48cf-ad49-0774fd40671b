#!/usr/bin/env python3
"""
Скрипт для сбора информации по футболу Казахстана
Фокус: последние 10 матчей с пенальти
"""

import os
import sqlite3
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

def setup_kazakhstan_config():
    """Настройка конфигурации для футбола Казахстана"""
    print("🇰🇿 Настройка конфигурации для футбола Казахстана...")
    
    os.makedirs('database', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Конфигурация для казахстанского футбола
    kazakhstan_config = """
# Конфигурация для футбола Казахстана
football:
  # Основные казахстанские лиги и турниры
  - "https://www.flashscore.com/football/kazakhstan/"
  - "https://www.flashscore.com/football/kazakhstan/premier-league/"
  - "https://www.flashscore.com/football/kazakhstan/first-division/"
  - "https://www.flashscore.com/football/kazakhstan/cup/"
  
  # Международные турниры с участием казахстанских команд
  - "https://www.flashscore.com/football/europe/champions-league/"
  - "https://www.flashscore.com/football/europe/europa-league/"
  - "https://www.flashscore.com/football/europe/conference-league/"
  
  # Сборная Казахстана
  - "https://www.flashscore.com/team/kazakhstan/"
"""
    
    with open('config/kazakhstan_football.yaml', 'w', encoding='utf-8') as f:
        f.write(kazakhstan_config)
    
    print("✅ Конфигурация для Казахстана создана")

def collect_kazakhstan_matches():
    """Сбор матчей казахстанского футбола"""
    print("\n⚽ Начинаем сбор матчей казахстанского футбола...")
    
    try:
        from flashscore_scraper import MatchIDScraper, MatchDataScraper
        
        # 1. Сначала собираем ID матчей
        print("1️⃣ Сбор ID матчей...")
        id_scraper = MatchIDScraper(
            config_path='config/kazakhstan_football.yaml',
            db_path='database/kazakhstan_match_ids.db'
        )
        
        id_result = id_scraper.scrape()
        print(f"✅ ID матчей собраны: {id_result}")
        
        # 2. Собираем детальные данные матчей
        print("\n2️⃣ Сбор детальных данных матчей...")
        data_scraper = MatchDataScraper(db_path='database/kazakhstan_matches.db')
        
        # Собираем данные за последние 30 дней для большего охвата
        data_result = data_scraper.update(days=30, batch_size=20, headless=True)
        print(f"✅ Данные матчей собраны: {data_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при сборе матчей: {e}")
        return False

def analyze_penalties_in_matches():
    """Анализ матчей с пенальти"""
    print("\n🥅 Анализ матчей с пенальти...")
    
    db_path = 'database/kazakhstan_matches.db'
    if not os.path.exists(db_path):
        print("❌ База данных с матчами не найдена")
        return []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Ищем матчи с дополнительными данными (где могут быть пенальти)
        query = """
        SELECT 
            flashscore_id,
            country,
            league,
            datetime,
            home_team,
            away_team,
            home_goals,
            away_goals,
            result,
            additional_data,
            created_at
        FROM match_data 
        WHERE 
            (country LIKE '%Kazakhstan%' OR country LIKE '%Казахстан%')
            OR (league LIKE '%Kazakhstan%' OR league LIKE '%Premier%' OR league LIKE '%Казахстан%')
            OR (home_team LIKE '%Astana%' OR home_team LIKE '%Kairat%' OR home_team LIKE '%Shakhter%' 
                OR home_team LIKE '%Tobol%' OR home_team LIKE '%Ordabasy%' OR home_team LIKE '%Aktobe%')
            OR (away_team LIKE '%Astana%' OR away_team LIKE '%Kairat%' OR away_team LIKE '%Shakhter%' 
                OR away_team LIKE '%Tobol%' OR away_team LIKE '%Ordabasy%' OR away_team LIKE '%Aktobe%')
        ORDER BY datetime DESC
        LIMIT 50
        """
        
        cursor.execute(query)
        matches = cursor.fetchall()
        
        print(f"📊 Найдено казахстанских матчей: {len(matches)}")
        
        penalty_matches = []
        
        for match in matches:
            (flashscore_id, country, league, match_datetime, home_team, away_team, 
             home_goals, away_goals, result, additional_data, created_at) = match
            
            # Проверяем дополнительные данные на наличие пенальти
            has_penalty = False
            penalty_info = ""
            
            if additional_data:
                try:
                    data = json.loads(additional_data)
                    # Ищем признаки пенальти в дополнительных данных
                    data_str = str(data).lower()
                    if any(keyword in data_str for keyword in ['penalty', 'пенальти', 'pen', 'pk']):
                        has_penalty = True
                        penalty_info = str(data)
                except:
                    pass
            
            # Также проверяем по счету (возможные пенальти в серии)
            if home_goals is not None and away_goals is not None:
                # Если счет необычный или есть дополнительное время
                if (home_goals > 5 or away_goals > 5) or (home_goals == away_goals and result != 0):
                    has_penalty = True
                    penalty_info += f" [Подозрение на пенальти по счету: {home_goals}-{away_goals}]"
            
            match_info = {
                'id': flashscore_id,
                'country': country,
                'league': league,
                'datetime': match_datetime,
                'home_team': home_team,
                'away_team': away_team,
                'score': f"{home_goals}-{away_goals}" if home_goals is not None else "N/A",
                'result': result,
                'has_penalty': has_penalty,
                'penalty_info': penalty_info,
                'additional_data': additional_data
            }
            
            if has_penalty:
                penalty_matches.append(match_info)
            
            # Добавляем все матчи для общего анализа
            if len(penalty_matches) < 20:  # Показываем до 20 матчей
                penalty_matches.append(match_info)
        
        conn.close()
        return penalty_matches
        
    except Exception as e:
        print(f"❌ Ошибка при анализе пенальти: {e}")
        return []

def display_penalty_matches(matches):
    """Отображение матчей с пенальти"""
    print(f"\n🎯 НАЙДЕНО МАТЧЕЙ: {len(matches)}")
    print("=" * 80)
    
    penalty_count = 0
    
    for i, match in enumerate(matches[:10], 1):  # Показываем последние 10
        print(f"\n{i}. 🏆 {match['home_team']} vs {match['away_team']}")
        print(f"   📅 Дата: {match['datetime']}")
        print(f"   🏟️  Лига: {match['league']} ({match['country']})")
        print(f"   ⚽ Счет: {match['score']}")
        
        if match['has_penalty']:
            penalty_count += 1
            print(f"   🥅 ПЕНАЛЬТИ: ДА ✅")
            if match['penalty_info']:
                print(f"   📝 Детали: {match['penalty_info'][:100]}...")
        else:
            print(f"   🥅 Пенальти: НЕТ")
        
        print(f"   🆔 ID: {match['id']}")
        print("-" * 60)
    
    print(f"\n📊 СТАТИСТИКА:")
    print(f"   • Всего проанализировано матчей: {len(matches)}")
    print(f"   • Матчей с пенальти: {penalty_count}")
    print(f"   • Процент матчей с пенальти: {penalty_count/min(len(matches), 10)*100:.1f}%")

def create_detailed_report():
    """Создание детального отчета"""
    print("\n📋 Создание детального отчета...")
    
    report_content = f"""
# ОТЧЕТ ПО ФУТБОЛУ КАЗАХСТАНА - АНАЛИЗ ПЕНАЛЬТИ
Дата создания: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ЦЕЛЬ АНАЛИЗА
Поиск последних 10 матчей казахстанского футбола, в которых были пенальти.

## ИСТОЧНИКИ ДАННЫХ
- FlashScore (через flashscore-scraper)
- Казахстанская Премьер-лига
- Первая лига Казахстана
- Кубок Казахстана
- Международные турниры с участием казахстанских команд

## КРИТЕРИИ ПОИСКА ПЕНАЛЬТИ
1. Анализ дополнительных данных матча (JSON)
2. Поиск ключевых слов: penalty, пенальти, pen, pk
3. Анализ необычных счетов
4. Матчи с дополнительным временем

## КАЗАХСТАНСКИЕ КОМАНДЫ В АНАЛИЗЕ
- ФК Астана
- Кайрат
- Шахтер Караганда
- Тобол
- Ордабасы
- Актобе
- И другие команды КПЛ

## СТРУКТУРА ДАННЫХ
Каждый матч содержит:
- ID матча в FlashScore
- Страна и лига
- Дата и время
- Команды
- Счет
- Результат
- Дополнительные данные (JSON)
- Информация о пенальти
"""
    
    with open('kazakhstan_football_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✅ Отчет сохранен в kazakhstan_football_report.md")

def main():
    """Главная функция"""
    print("🇰🇿 АНАЛИЗ ФУТБОЛА КАЗАХСТАНА - ПОИСК ПЕНАЛЬТИ")
    print("=" * 60)
    print("Цель: Найти последние 10 матчей с пенальти")
    print("=" * 60)
    
    # 1. Настройка конфигурации
    setup_kazakhstan_config()
    
    # 2. Сбор данных
    print("\n🔄 Начинаем сбор данных...")
    success = collect_kazakhstan_matches()
    
    if not success:
        print("❌ Не удалось собрать данные. Попробуем проанализировать существующие...")
    
    # 3. Анализ пенальти
    matches = analyze_penalties_in_matches()
    
    if matches:
        # 4. Отображение результатов
        display_penalty_matches(matches)
        
        # 5. Создание отчета
        create_detailed_report()
        
        print("\n🎉 АНАЛИЗ ЗАВЕРШЕН!")
        print("📁 Файлы созданы:")
        print("   • database/kazakhstan_matches.db - база данных матчей")
        print("   • config/kazakhstan_football.yaml - конфигурация")
        print("   • kazakhstan_football_report.md - детальный отчет")
        
    else:
        print("\n❌ Не найдено данных для анализа")
        print("💡 Возможные причины:")
        print("   • Нет подключения к интернету")
        print("   • FlashScore заблокировал запросы")
        print("   • Нужна более детальная конфигурация")
        print("   • Требуется установка браузера (Chrome/Firefox)")
    
    print("\n⚠️  ВАЖНО:")
    print("Этот скрипт делает реальные запросы к FlashScore.")
    print("Соблюдайте их условия использования!")

if __name__ == "__main__":
    main()
